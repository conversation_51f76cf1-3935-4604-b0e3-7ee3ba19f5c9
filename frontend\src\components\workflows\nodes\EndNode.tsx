'use client'

import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Square } from 'lucide-react'

interface EndNodeData {
  label: string
}

export const EndNode = memo(({ data, selected }: NodeProps<EndNodeData>) => {
  return (
    <div className={`
      relative bg-white dark:bg-gray-800 border-2 rounded-full shadow-lg w-16 h-16 flex items-center justify-center
      ${selected ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'}
      transition-all duration-200 hover:shadow-xl
    `}>
      <div className="p-2 bg-red-100 dark:bg-red-900 rounded-full">
        <Square className="w-6 h-6 text-red-600 dark:text-red-400 fill-current" />
      </div>
      
      <Handle type="target" position={Position.Top} className="w-3 h-3" />
    </div>
  )
})