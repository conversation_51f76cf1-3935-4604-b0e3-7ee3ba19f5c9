import { Controller, Post, Get, Body, Param, UseGuards } from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'
import { BusinessService } from './business.service'

@Controller('business')
@UseGuards(AuthGuard('jwt'))
export class BusinessController {
  constructor(private readonly businessService: BusinessService) {}

  @Post('interview/start')
  startInterview(@Body('tenantId') tenantId: string) {
    return this.businessService.startInterview(tenantId)
  }

  @Post('interview/response')
  processResponse(@Body() data: any) {
    return this.businessService.processInterviewResponse(
      data.tenantId,
      data.sessionId,
      data.response
    )
  }

  @Post('generate')
  generateSystem(@Body() data: any) {
    return this.businessService.generateBusinessSystem(data.tenantId, data.interviewData)
  }

  @Get('status/:tenantId')
  getStatus(@Param('tenantId') tenantId: string) {
    return this.businessService.getSystemStatus(tenantId)
  }
}