import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm'

@Entity('workflow_executions')
export class WorkflowExecution {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  workflowId: string

  @Column()
  tenantId: string

  @Column({ type: 'enum', enum: ['pending', 'running', 'completed', 'failed', 'cancelled'] })
  status: string

  @Column('json', { nullable: true })
  context: Record<string, any>

  @Column('json', { nullable: true })
  result: Record<string, any>

  @Column('text', { nullable: true })
  error: string

  @Column()
  startedAt: Date

  @Column({ nullable: true })
  completedAt: Date

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}