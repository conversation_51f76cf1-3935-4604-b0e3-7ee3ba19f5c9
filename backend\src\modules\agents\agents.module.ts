import { Modu<PERSON> } from '@nestjs/common';
import { AgentsService } from './agents.service';
import { AgentsController } from './agents.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { AIProviderModule } from '../../providers/ai/ai-provider.module';

@Module({
  imports: [PrismaModule, AIProviderModule],
  controllers: [AgentsController],
  providers: [AgentsService],
  exports: [AgentsService],
})
export class AgentsModule {}