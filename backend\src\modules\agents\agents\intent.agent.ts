import { Injectable } from '@nestjs/common'

export interface IntentResult {
  type: string
  confidence: number
  entities: Record<string, any>
  category: string
}

@Injectable()
export class IntentAgent {
  private intentPatterns = {
    greeting: ['hello', 'hi', 'hey', 'good morning', 'good afternoon'],
    question: ['what', 'how', 'when', 'where', 'why', 'who'],
    request: ['can you', 'please', 'would you', 'could you'],
    business_query: ['customer', 'order', 'invoice', 'report', 'data'],
    workflow: ['process', 'workflow', 'automation', 'task'],
    help: ['help', 'support', 'assistance', 'guide'],
  }

  async classifyIntent(message: string, context: any): Promise<IntentResult> {
    const lowerMessage = message.toLowerCase()
    
    // Simple pattern matching for demo
    let bestMatch = { type: 'general', confidence: 0.5 }
    
    for (const [intentType, patterns] of Object.entries(this.intentPatterns)) {
      const matches = patterns.filter(pattern => lowerMessage.includes(pattern))
      if (matches.length > 0) {
        const confidence = matches.length / patterns.length
        if (confidence > bestMatch.confidence) {
          bestMatch = { type: intentType, confidence }
        }
      }
    }

    // Extract entities (simplified)
    const entities = this.extractEntities(message)

    return {
      type: bestMatch.type,
      confidence: Math.min(bestMatch.confidence + 0.3, 0.95),
      entities,
      category: this.categorizeIntent(bestMatch.type),
    }
  }

  private extractEntities(message: string): Record<string, any> {
    const entities: Record<string, any> = {}
    
    // Extract dates
    const datePattern = /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g
    const dates = message.match(datePattern)
    if (dates) entities.dates = dates

    // Extract numbers
    const numberPattern = /\b\d+\b/g
    const numbers = message.match(numberPattern)
    if (numbers) entities.numbers = numbers.map(n => parseInt(n))

    // Extract emails
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
    const emails = message.match(emailPattern)
    if (emails) entities.emails = emails

    return entities
  }

  private categorizeIntent(intentType: string): string {
    const categories = {
      greeting: 'social',
      question: 'informational',
      request: 'actionable',
      business_query: 'business',
      workflow: 'automation',
      help: 'support',
      general: 'general',
    }

    return categories[intentType] || 'general'
  }
}