import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm'

@Entity('knowledge_documents')
export class KnowledgeDocument {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  filename: string

  @Column()
  originalName: string

  @Column()
  mimeType: string

  @Column()
  size: number

  @Column()
  tenantId: string

  @Column()
  uploadedBy: string

  @Column('text')
  content: string

  @Column('json', { nullable: true })
  chunks: string[]

  @Column('json', { nullable: true })
  metadata: Record<string, any>

  @Column({ type: 'enum', enum: ['processing', 'completed', 'failed'], default: 'processing' })
  status: string

  @Column({ nullable: true })
  processedAt: Date

  @Column('simple-array', { nullable: true })
  tags: string[]

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}