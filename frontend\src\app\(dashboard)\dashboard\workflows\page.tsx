'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Workflow, 
  Play, 
  Edit, 
  Trash2, 
  Copy,
  Eye,
  Pause,
  Users,
  Globe,
  Lock
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { WorkflowPreviewDialog } from '@/components/workflows/WorkflowPreviewDialog'
import { apiClient } from '@/lib/api-client'
import { formatDistanceToNow } from 'date-fns'
import toast from 'react-hot-toast'

interface WorkflowItem {
  id: string
  name: string
  description: string
  isActive: boolean
  isPublic: boolean
  version: number
  creator: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  _count: {
    nodes: number
    executions: number
  }
  createdAt: string
  updatedAt: string
}

const statusFilters = [
  { value: 'all', label: 'All Status' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'public', label: 'Public' },
  { value: 'private', label: 'Private' },
]

export default function WorkflowsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [previewingWorkflow, setPreviewingWorkflow] = useState<WorkflowItem | null>(null)
  const router = useRouter()

  const { data: workflows, isLoading, refetch } = useQuery({
    queryKey: ['workflows', statusFilter],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (statusFilter === 'public') params.append('public', 'true')
      
      const response = await apiClient.get(`/api/workflows?${params.toString()}`)
      return response.data
    }
  })

  const filteredWorkflows = workflows?.filter((workflow: WorkflowItem) => {
    const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workflow.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && workflow.isActive) ||
                         (statusFilter === 'inactive' && !workflow.isActive) ||
                         (statusFilter === 'public' && workflow.isPublic) ||
                         (statusFilter === 'private' && !workflow.isPublic)

    return matchesSearch && matchesStatus
  })

  const handleExecuteWorkflow = async (workflowId: string) => {
    try {
      const response = await apiClient.post(`/api/workflows/${workflowId}/execute`, {
        input: {}
      })
      toast.success('Workflow execution started')
      router.push(`/dashboard/executions/${response.data.executionId}`)
    } catch (error) {
      toast.error('Failed to execute workflow')
    }
  }

  const handleToggleWorkflow = async (workflowId: string, isActive: boolean) => {
    try {
      await apiClient.patch(`/api/workflows/${workflowId}`, {
        isActive: !isActive
      })
      toast.success(`Workflow ${!isActive ? 'activated' : 'deactivated'} successfully`)
      refetch()
    } catch (error) {
      toast.error('Failed to update workflow status')
    }
  }

  const handleDeleteWorkflow = async (workflowId: string) => {
    if (confirm('Are you sure you want to delete this workflow?')) {
      try {
        await apiClient.delete(`/api/workflows/${workflowId}`)
        toast.success('Workflow deleted successfully')
        refetch()
      } catch (error) {
        toast.error('Failed to delete workflow')
      }
    }
  }

  const handleCloneWorkflow = async (workflow: WorkflowItem) => {
    try {
      const cloneData = {
        ...workflow,
        name: `${workflow.name} (Copy)`,
        id: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        version: 1,
      }
      await apiClient.post('/api/workflows', cloneData)
      toast.success('Workflow cloned successfully')
      refetch()
    } catch (error) {
      toast.error('Failed to clone workflow')
    }
  }

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Workflows</h1>
          <Button disabled>
            <Plus className="w-4 h-4 mr-2" />
            Create Workflow
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-3/4" />
                <div className="h-4 bg-gray-200 rounded w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded" />
                  <div className="h-4 bg-gray-200 rounded w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Workflows</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your automated business processes
          </p>
        </div>
        <Button onClick={() => router.push('/dashboard/workflows/new')}>
          <Plus className="w-4 h-4 mr-2" />
          Create Workflow
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search workflows..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-40">
            <Filter className="w-4 h-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {statusFilters.map((status) => (
              <SelectItem key={status.value} value={status.value}>
                {status.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Workflows Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredWorkflows?.map((workflow: WorkflowItem) => (
          <Card key={workflow.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                    <Workflow className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate flex items-center gap-2">
                      {workflow.name}
                      {workflow.isPublic ? (
                        <Globe className="w-4 h-4 text-green-600" />
                      ) : (
                        <Lock className="w-4 h-4 text-gray-400" />
                      )}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      {workflow.isActive ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="secondary">Inactive</Badge>
                      )}
                      <Badge variant="outline">
                        v{workflow.version}
                      </Badge>
                    </div>
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem 
                      onClick={() => handleExecuteWorkflow(workflow.id)}
                      disabled={!workflow.isActive}
                    >
                      <Play className="w-4 h-4 mr-2" />
                      Execute
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setPreviewingWorkflow(workflow)}>
                      <Eye className="w-4 h-4 mr-2" />
                      Preview
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => router.push(`/dashboard/workflows/${workflow.id}`)}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleCloneWorkflow(workflow)}>
                      <Copy className="w-4 h-4 mr-2" />
                      Clone
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => handleToggleWorkflow(workflow.id, workflow.isActive)}
                    >
                      {workflow.isActive ? (
                        <>
                          <Pause className="w-4 h-4 mr-2" />
                          Deactivate
                        </>
                      ) : (
                        <>
                          <Play className="w-4 h-4 mr-2" />
                          Activate
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleDeleteWorkflow(workflow.id)}
                      className="text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <CardDescription className="line-clamp-2">
                {workflow.description || 'No description provided'}
              </CardDescription>

              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Nodes:</span>
                  <span className="font-medium">{workflow._count?.nodes || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Executions:</span>
                  <span className="font-medium">{workflow._count?.executions || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Updated:</span>
                  <span className="font-medium">
                    {formatDistanceToNow(new Date(workflow.updatedAt), { addSuffix: true })}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between pt-3 border-t">
                <div className="flex items-center gap-2">
                  <Avatar className="w-6 h-6">
                    <AvatarImage src={workflow.creator.avatar} />
                    <AvatarFallback className="text-xs">
                      {getUserInitials(workflow.creator.name)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {workflow.creator.name}
                  </span>
                </div>
                <span className="text-xs text-gray-500">
                  {formatDistanceToNow(new Date(workflow.createdAt), { addSuffix: true })}
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredWorkflows?.length === 0 && (
        <div className="text-center py-12">
          <Workflow className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No workflows found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {searchTerm || statusFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Get started by creating your first workflow'
            }
          </p>
          <Button onClick={() => router.push('/dashboard/workflows/new')}>
            <Plus className="w-4 h-4 mr-2" />
            Create Workflow
          </Button>
        </div>
      )}

      {/* Workflow Preview Dialog */}
      {previewingWorkflow && (
        <WorkflowPreviewDialog
          workflow={previewingWorkflow}
          open={!!previewingWorkflow}
          onOpenChange={(open) => !open && setPreviewingWorkflow(null)}
        />
      )}
    </div>
  )
}