import { 
  Controller, 
  Get, 
  Post, 
  Delete, 
  Param, 
  Query, 
  UseGuards, 
  UseInterceptors, 
  UploadedFile 
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { AuthGuard } from '@nestjs/passport'
import { KnowledgeService } from './knowledge.service'

@Controller('knowledge')
@UseGuards(AuthGuard('jwt'))
export class KnowledgeController {
  constructor(private readonly knowledgeService: KnowledgeService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Query('tenantId') tenantId: string,
    @Query('userId') userId: string,
  ) {
    return this.knowledgeService.uploadDocument(file, tenantId, userId)
  }

  @Get()
  findAll(@Query('tenantId') tenantId: string) {
    return this.knowledgeService.findAll(tenantId)
  }

  @Get('search')
  search(@Query('q') query: string, @Query('tenantId') tenantId: string) {
    return this.knowledgeService.search(query, tenantId)
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.knowledgeService.findOne(id)
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.knowledgeService.remove(id)
  }
}