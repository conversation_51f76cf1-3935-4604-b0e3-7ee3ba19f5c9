'use client'

import { useCallback, useState } from 'react'
import { 
  EdgeProps, 
  getBezierPath, 
  EdgeLabelRenderer,
  BaseEdge,
  useReactFlow
} from 'reactflow'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { 
  AlertTriangle, 
  Check, 
  X, 
  RotateCcw, 
  Settings,
  Trash2
} from 'lucide-react'

export interface CustomEdgeData {
  condition?: {
    type: 'success' | 'failure' | 'custom'
    label?: string
    expression?: string
  }
  retryCount?: number
  maxRetries?: number
  status?: 'pending' | 'running' | 'success' | 'failure'
  animated?: boolean
}

export function CustomEdge({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
  selected,
}: EdgeProps<CustomEdgeData>) {
  const { setEdges } = useReactFlow()
  const [showMenu, setShowMenu] = useState(false)

  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  })

  const onEdgeClick = useCallback((event: React.MouseEvent) => {
    event.stopPropagation()
    setShowMenu(true)
  }, [])

  const onDeleteEdge = useCallback(() => {
    setEdges((edges) => edges.filter((edge) => edge.id !== id))
  }, [id, setEdges])

  const getEdgeColor = () => {
    if (data?.status === 'success') return '#10B981'
    if (data?.status === 'failure') return '#EF4444'
    if (data?.status === 'running') return '#F59E0B'
    if (data?.condition?.type === 'success') return '#10B981'
    if (data?.condition?.type === 'failure') return '#EF4444'
    return '#6B7280'
  }

  const getStatusIcon = () => {
    switch (data?.status) {
      case 'success':
        return <Check className="w-3 h-3" />
      case 'failure':
        return <X className="w-3 h-3" />
      case 'running':
        return <div className="w-3 h-3 rounded-full bg-yellow-500 animate-pulse" />
      default:
        return null
    }
  }

  const edgeColor = getEdgeColor()
  const isAnimated = data?.animated || data?.status === 'running'

  return (
    <>
      <BaseEdge
        path={edgePath}
        markerEnd={markerEnd}
        style={{
          ...style,
          stroke: edgeColor,
          strokeWidth: selected ? 3 : 2,
          strokeDasharray: isAnimated ? '5,5' : 'none',
          animation: isAnimated ? 'dashdraw 1s linear infinite' : 'none',
        }}
      />

      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            fontSize: 12,
            pointerEvents: 'all',
          }}
          className="nodrag nopan"
        >
          <DropdownMenu open={showMenu} onOpenChange={setShowMenu}>
            <DropdownMenuTrigger asChild>
              <div
                onClick={onEdgeClick}
                className={`
                  flex items-center gap-1 px-2 py-1 rounded-full border cursor-pointer
                  ${selected ? 'bg-blue-100 border-blue-300' : 'bg-white border-gray-300'}
                  hover:bg-gray-50 transition-colors shadow-sm
                `}
              >
                {getStatusIcon()}
                
                {data?.condition?.label && (
                  <span className="text-xs font-medium">
                    {data.condition.label}
                  </span>
                )}

                {data?.retryCount !== undefined && data?.maxRetries && (
                  <Badge variant="outline" className="text-xs ml-1">
                    <RotateCcw className="w-2 h-2 mr-1" />
                    {data.retryCount}/{data.maxRetries}
                  </Badge>
                )}

                {data?.condition?.type === 'custom' && (
                  <AlertTriangle className="w-3 h-3 text-amber-500" />
                )}
              </div>
            </DropdownMenuTrigger>

            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => console.log('Edit condition')}>
                <Settings className="w-4 h-4 mr-2" />
                Edit Condition
              </DropdownMenuItem>
              
              {data?.condition?.type === 'custom' && (
                <DropdownMenuItem onClick={() => console.log('Edit expression')}>
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  Edit Expression
                </DropdownMenuItem>
              )}
              
              <DropdownMenuItem 
                onClick={onDeleteEdge}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Edge
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </EdgeLabelRenderer>

      <style jsx>{`
        @keyframes dashdraw {
          from {
            stroke-dashoffset: 10;
          }
          to {
            stroke-dashoffset: 0;
          }
        }
      `}</style>
    </>
  )
}