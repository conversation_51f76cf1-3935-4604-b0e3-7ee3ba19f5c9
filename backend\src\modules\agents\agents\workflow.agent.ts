import { Injectable } from '@nestjs/common'

export interface WorkflowStep {
  id: string
  type: string
  name: string
  config: Record<string, any>
  dependencies: string[]
}

export interface WorkflowResult {
  executed: boolean
  workflowId?: string
  steps: Array<{
    stepId: string
    status: 'completed' | 'failed' | 'skipped'
    output: any
    error?: string
  }>
  totalSteps: number
}

@Injectable()
export class WorkflowAgent {
  private workflows = new Map<string, WorkflowStep[]>([
    ['customer_onboarding', [
      {
        id: 'step1',
        type: 'validation',
        name: 'Validate Customer Data',
        config: { required_fields: ['email', 'name'] },
        dependencies: [],
      },
      {
        id: 'step2',
        type: 'api_call',
        name: 'Create Customer Record',
        config: { endpoint: '/api/customers', method: 'POST' },
        dependencies: ['step1'],
      },
      {
        id: 'step3',
        type: 'notification',
        name: 'Send Welcome Email',
        config: { template: 'welcome', delay: 0 },
        dependencies: ['step2'],
      },
    ]],
    ['order_processing', [
      {
        id: 'step1',
        type: 'validation',
        name: 'Validate Order',
        config: { check_inventory: true },
        dependencies: [],
      },
      {
        id: 'step2',
        type: 'payment',
        name: 'Process Payment',
        config: { gateway: 'stripe' },
        dependencies: ['step1'],
      },
      {
        id: 'step3',
        type: 'fulfillment',
        name: 'Prepare for Shipping',
        config: { warehouse: 'main' },
        dependencies: ['step2'],
      },
    ]],
  ])

  async executeWorkflow(intent: any, tools: any[], context: any): Promise<WorkflowResult> {
    const workflowId = this.determineWorkflow(intent, context)
    
    if (!workflowId) {
      return {
        executed: false,
        steps: [],
        totalSteps: 0,
      }
    }

    const workflow = this.workflows.get(workflowId)
    if (!workflow) {
      return {
        executed: false,
        steps: [],
        totalSteps: 0,
      }
    }

    const executionResults = []
    const executedSteps = new Set<string>()

    // Execute workflow steps in dependency order
    for (const step of workflow) {
      if (this.canExecuteStep(step, executedSteps)) {
        const result = await this.executeStep(step, context)
        executionResults.push(result)
        
        if (result.status === 'completed') {
          executedSteps.add(step.id)
        }
      }
    }

    return {
      executed: true,
      workflowId,
      steps: executionResults,
      totalSteps: workflow.length,
    }
  }

  private determineWorkflow(intent: any, context: any): string | null {
    const message = context.message.toLowerCase()

    if (message.includes('customer') && message.includes('new')) {
      return 'customer_onboarding'
    }
    
    if (message.includes('order') && (message.includes('create') || message.includes('process'))) {
      return 'order_processing'
    }

    return null
  }

  private canExecuteStep(step: WorkflowStep, executedSteps: Set<string>): boolean {
    return step.dependencies.every(dep => executedSteps.has(dep))
  }

  private async executeStep(step: WorkflowStep, context: any): Promise<any> {
    try {
      // Mock step execution based on type
      switch (step.type) {
        case 'validation':
          return {
            stepId: step.id,
            status: 'completed',
            output: { validated: true, message: 'Validation passed' },
          }
        
        case 'api_call':
          return {
            stepId: step.id,
            status: 'completed',
            output: { 
              apiResponse: 'Success', 
              data: { id: 'generated-id' },
            },
          }
        
        case 'notification':
          return {
            stepId: step.id,
            status: 'completed',
            output: { sent: true, messageId: 'msg-123' },
          }
        
        case 'payment':
          return {
            stepId: step.id,
            status: 'completed',
            output: { 
              transactionId: 'txn-456', 
              amount: 100, 
              status: 'paid',
            },
          }
        
        case 'fulfillment':
          return {
            stepId: step.id,
            status: 'completed',
            output: { 
              fulfillmentId: 'ful-789', 
              estimatedShipping: '2-3 days',
            },
          }
        
        default:
          return {
            stepId: step.id,
            status: 'completed',
            output: { message: 'Step executed successfully' },
          }
      }
    } catch (error) {
      return {
        stepId: step.id,
        status: 'failed',
        output: null,
        error: error.message,
      }
    }
  }
}