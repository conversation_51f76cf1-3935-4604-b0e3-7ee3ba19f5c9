'use client'

import { useEffect, useState } from 'react'
import { Bot, Workflow, Wrench, Activity } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface Stats {
  agents: number
  workflows: number
  tools: number
  executions: number
}

export function DashboardStats() {
  const [stats, setStats] = useState<Stats>({
    agents: 0,
    workflows: 0,
    tools: 0,
    executions: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadStats() {
      try {
        // In production, this would be real API calls
        // Simulating API call delay
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        setStats({
          agents: 12,
          workflows: 8,
          tools: 25,
          executions: 1284,
        })
      } catch (error) {
        console.error('Failed to load stats:', error)
      } finally {
        setLoading(false)
      }
    }

    loadStats()
  }, [])

  const statItems = [
    {
      title: 'Active Agents',
      value: stats.agents,
      icon: Bo<PERSON>,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: '+12%',
      changeColor: 'text-green-600',
    },
    {
      title: 'Workflows',
      value: stats.workflows,
      icon: Workflow,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: '+8%',
      changeColor: 'text-green-600',
    },
    {
      title: 'Tools',
      value: stats.tools,
      icon: Wrench,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      change: '+15%',
      changeColor: 'text-green-600',
    },
    {
      title: 'Executions Today',
      value: stats.executions,
      icon: Activity,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: '+23%',
      changeColor: 'text-green-600',
    },
  ]

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-24"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-12"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statItems.map((stat, index) => {
        const Icon = stat.icon
        return (
          <Card key={index} className="transition-all duration-200 hover:shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${stat.bgColor} dark:bg-gray-800`}>
                <Icon className={`w-4 h-4 ${stat.color} dark:text-gray-400`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {stat.value.toLocaleString()}
              </div>
              <p className="text-xs text-gray-600 dark:text-gray-400 flex items-center mt-1">
                <span className={`${stat.changeColor} font-medium`}>
                  {stat.change}
                </span>
                <span className="ml-1">from last month</span>
              </p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}