'use client'

import { useState } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Plus, <PERSON><PERSON>, Wrench, Workflow, Zap } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Sheet, 
  SheetContent, 
  SheetDescription, 
  SheetHeader, 
  SheetTitle, 
  SheetTrigger 
} from '@/components/ui/sheet'
import { 
  Too<PERSON>ip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from '@/components/ui/tooltip'
import { QuickCreateAgent } from '@/components/quick-create/QuickCreateAgent'
import { QuickCreateTool } from '@/components/quick-create/QuickCreateTool'
import { QuickCreateWorkflow } from '@/components/quick-create/QuickCreateWorkflow'

const quickActions = [
  {
    id: 'agent',
    title: 'Create Agent',
    description: 'Build an AI agent with custom personality',
    icon: Bo<PERSON>,
    color: 'text-blue-600 bg-blue-100 hover:bg-blue-200',
    href: '/dashboard/agents/new',
  },
  {
    id: 'tool',
    title: 'Add Tool',
    description: 'Connect external APIs and services',
    icon: Wrench,
    color: 'text-orange-600 bg-orange-100 hover:bg-orange-200',
    href: '/dashboard/tools/new',
  },
  {
    id: 'workflow',
    title: 'Build Workflow',
    description: 'Create automated business processes',
    icon: Workflow,
    color: 'text-purple-600 bg-purple-100 hover:bg-purple-200',
    href: '/dashboard/workflows/new',
  },
  {
    id: 'execute',
    title: 'Quick Execute',
    description: 'Run a workflow with test data',
    icon: Zap,
    color: 'text-green-600 bg-green-100 hover:bg-green-200',
    href: '/dashboard/executions/new',
  },
]

export function QuickActions() {
  const [openSheet, setOpenSheet] = useState<string | null>(null)
  const router = useRouter()

  const handleQuickAction = (action: any) => {
    if (action.id === 'execute') {
      router.push(action.href)
    } else {
      setOpenSheet(action.id)
    }
  }

  const renderQuickCreateContent = () => {
    switch (openSheet) {
      case 'agent':
        return <QuickCreateAgent onSuccess={() => setOpenSheet(null)} />
      case 'tool':
        return <QuickCreateTool onSuccess={() => setOpenSheet(null)} />
      case 'workflow':
        return <QuickCreateWorkflow onSuccess={() => setOpenSheet(null)} />
      default:
        return null
    }
  }

  const getSheetTitle = () => {
    const action = quickActions.find(a => a.id === openSheet)
    return action?.title || 'Quick Create'
  }

  const getSheetDescription = () => {
    const action = quickActions.find(a => a.id === openSheet)
    return action?.description || ''
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Quick Actions</CardTitle>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 gap-3">
          <TooltipProvider>
            {quickActions.map((action) => {
              const Icon = action.icon
              
              return (
                <Tooltip key={action.id}>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      className="h-20 flex flex-col items-center justify-center gap-2 hover:scale-105 transition-transform"
                      onClick={() => handleQuickAction(action)}
                    >
                      <div className={`p-2 rounded-lg ${action.color}`}>
                        <Icon className="w-5 h-5" />
                      </div>
                      <span className="text-xs font-medium">{action.title}</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{action.description}</p>
                  </TooltipContent>
                </Tooltip>
              )
            })}
          </TooltipProvider>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button 
            variant="outline" 
            className="w-full"
            onClick={() => router.push('/dashboard/templates')}
          >
            <Plus className="w-4 h-4 mr-2" />
            Browse Templates
          </Button>
        </div>
      </CardContent>

      <Sheet open={!!openSheet} onOpenChange={(open) => !open && setOpenSheet(null)}>
        <SheetContent className="w-[400px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle>{getSheetTitle()}</SheetTitle>
            <SheetDescription>{getSheetDescription()}</SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            {renderQuickCreateContent()}
          </div>
        </SheetContent>
      </Sheet>
    </Card>
  )
}