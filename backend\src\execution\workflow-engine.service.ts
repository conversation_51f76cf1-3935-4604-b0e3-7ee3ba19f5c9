import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AIProviderService } from '../providers/ai/ai-provider.service';
import { ToolExecutorService } from './tool-executor.service';
import { WebSocketGateway } from '../websocket/websocket.gateway';
import { Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bull';

export interface WorkflowContext {
  executionId: string;
  variables: Record<string, any>;
  userId?: string;
}

export interface NodeExecutionResult {
  success: boolean;
  output: any;
  error?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class WorkflowEngineService {
  private readonly logger = new Logger(WorkflowEngineService.name);

  constructor(
    private prisma: PrismaService,
    private aiProvider: AIProviderService,
    private toolExecutor: ToolExecutorService,
    private wsGateway: WebSocketGateway,
    @InjectQueue('workflow') private workflowQueue: Queue,
  ) {}

  async executeWorkflow(
    workflowId: string,
    input: any,
    userId?: string,
  ): Promise<string> {
    const workflow = await this.prisma.workflow.findUnique({
      where: { id: workflowId },
      include: {
        nodes: {
          include: {
            agent: true,
            tool: true,
          },
        },
        edges: true,
      },
    });

    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    // Create execution record
    const execution = await this.prisma.workflowExecution.create({
      data: {
        workflowId,
        userId,
        input,
        status: 'RUNNING',
        context: {},
      },
    });

    // Start execution in background
    await this.workflowQueue.add('execute', {
      executionId: execution.id,
      workflowId,
      input,
      userId,
    });

    // Notify clients
    this.wsGateway.notifyWorkflowStarted(execution.id, workflowId);

    return execution.id;
  }

  async processExecution(executionId: string): Promise<void> {
    const execution = await this.prisma.workflowExecution.findUnique({
      where: { id: executionId },
      include: {
        workflow: {
          include: {
            nodes: {
              include: {
                agent: true,
                tool: true,
              },
            },
            edges: true,
          },
        },
      },
    });

    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const context: WorkflowContext = {
      executionId,
      variables: { ...execution.input, ...execution.context },
      userId: execution.userId,
    };

    try {
      // Find start nodes
      const startNodes = execution.workflow.nodes.filter(
        node => node.type === 'START'
      );

      if (startNodes.length === 0) {
        throw new Error('No start node found in workflow');
      }

      // Execute from start nodes
      for (const startNode of startNodes) {
        await this.executeNodePath(execution.workflow, startNode.id, context);
      }

      // Mark execution as completed
      await this.prisma.workflowExecution.update({
        where: { id: executionId },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
          output: context.variables,
        },
      });

      this.wsGateway.notifyWorkflowCompleted(executionId, context.variables);

    } catch (error) {
      this.logger.error(`Workflow execution failed:`, error);

      await this.prisma.workflowExecution.update({
        where: { id: executionId },
        data: {
          status: 'FAILED',
          completedAt: new Date(),
          error: error.message,
        },
      });

      this.wsGateway.notifyWorkflowFailed(executionId, error.message);
    }
  }

  private async executeNodePath(
    workflow: any,
    nodeId: string,
    context: WorkflowContext,
  ): Promise<void> {
    const node = workflow.nodes.find(n => n.id === nodeId);
    if (!node) return;

    this.logger.log(`Executing node ${nodeId} of type ${node.type}`);

    // Execute current node
    const result = await this.executeNode(node, context);

    // Record node execution
    await this.prisma.nodeExecution.create({
      data: {
        executionId: context.executionId,
        nodeId,
        status: result.success ? 'COMPLETED' : 'FAILED',
        input: node.data,
        output: result.output,
        error: result.error,
        completedAt: new Date(),
      },
    });

    // Notify progress
    this.wsGateway.notifyNodeCompleted(context.executionId, nodeId, result);

    if (!result.success) {
      throw new Error(`Node ${nodeId} failed: ${result.error}`);
    }

    // Update context with node output
    if (result.output) {
      context.variables = { ...context.variables, ...result.output };
    }

    // Find next nodes
    const outgoingEdges = workflow.edges.filter(
      edge => edge.sourceNodeId === nodeId
    );

    for (const edge of outgoingEdges) {
      // Check edge condition if present
      if (edge.condition && !this.evaluateCondition(edge.condition, context)) {
        continue;
      }

      // Execute next node
      await this.executeNodePath(workflow, edge.targetNodeId, context);
    }
  }

  private async executeNode(
    node: any,
    context: WorkflowContext,
  ): Promise<NodeExecutionResult> {
    try {
      switch (node.type) {
        case 'START':
          return { success: true, output: { started: true } };

        case 'END':
          return { success: true, output: { completed: true } };

        case 'AGENT':
          return await this.executeAgentNode(node, context);

        case 'TOOL':
          return await this.executeToolNode(node, context);

        case 'CONDITION':
          return await this.executeConditionNode(node, context);

        case 'DELAY':
          return await this.executeDelayNode(node, context);

        case 'WEBHOOK':
          return await this.executeWebhookNode(node, context);

        default:
          throw new Error(`Unsupported node type: ${node.type}`);
      }
    } catch (error) {
      return {
        success: false,
        output: null,
        error: error.message,
      };
    }
  }

  private async executeAgentNode(
    node: any,
    context: WorkflowContext,
  ): Promise<NodeExecutionResult> {
    if (!node.agent) {
      throw new Error('Agent not found for agent node');
    }

    const agent = node.agent;
    const nodeData = node.data;

    // Prepare prompt with context variables
    let prompt = nodeData.prompt || 'Process the following data:';
    
    // Replace variables in prompt
    prompt = this.replaceVariables(prompt, context.variables);

    // Create messages
    const messages = [
      { role: 'system' as const, content: agent.personality?.systemPrompt || 'You are a helpful AI assistant.' },
      { role: 'user' as const, content: prompt },
    ];

    // Execute AI request
    const response = await this.aiProvider.generateResponse(
      nodeData.provider || 'openai',
      agent.model,
      messages,
      {
        temperature: agent.temperature,
        maxTokens: agent.maxTokens,
      }
    );

    // Record agent execution
    await this.prisma.agentExecution.create({
      data: {
        agentId: agent.id,
        executionId: context.executionId,
        prompt,
        response: response.content,
        metadata: { usage: response.usage },
        status: 'COMPLETED',
        tokensUsed: response.usage?.totalTokens,
        completedAt: new Date(),
      },
    });

    return {
      success: true,
      output: {
        response: response.content,
        usage: response.usage,
      },
      metadata: {
        model: response.model,
        finishReason: response.finishReason,
      },
    };
  }

  private async executeToolNode(
    node: any,
    context: WorkflowContext,
  ): Promise<NodeExecutionResult> {
    if (!node.tool) {
      throw new Error('Tool not found for tool node');
    }

    const tool = node.tool;
    const nodeData = node.data;

    // Prepare input parameters
    let input = nodeData.input || {};
    
    // Replace variables in input
    input = this.replaceObjectVariables(input, context.variables);

    // Execute tool
    const result = await this.toolExecutor.executeTool(tool.id, input, context.executionId);

    return {
      success: result.success,
      output: result.output,
      error: result.error,
      metadata: result.metadata,
    };
  }

  private async executeConditionNode(
    node: any,
    context: WorkflowContext,
  ): Promise<NodeExecutionResult> {
    const condition = node.data.condition;
    const result = this.evaluateCondition(condition, context);

    return {
      success: true,
      output: { conditionMet: result },
    };
  }

  private async executeDelayNode(
    node: any,
    context: WorkflowContext,
  ): Promise<NodeExecutionResult> {
    const delayMs = node.data.delay || 1000;
    
    await new Promise(resolve => setTimeout(resolve, delayMs));

    return {
      success: true,
      output: { delayed: delayMs },
    };
  }

  private async executeWebhookNode(
    node: any,
    context: WorkflowContext,
  ): Promise<NodeExecutionResult> {
    const { url, method = 'POST', headers = {}, body } = node.data;

    // Replace variables in URL and body
    const processedUrl = this.replaceVariables(url, context.variables);
    const processedBody = this.replaceObjectVariables(body, context.variables);

    const response = await fetch(processedUrl, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      body: method !== 'GET' ? JSON.stringify(processedBody) : undefined,
    });

    const responseData = await response.json();

    return {
      success: response.ok,
      output: responseData,
      error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`,
    };
  }

  private evaluateCondition(condition: any, context: WorkflowContext): boolean {
    if (!condition) return true;

    try {
      // Simple condition evaluation
      const { variable, operator, value } = condition;
      const actualValue = this.getNestedValue(context.variables, variable);

      switch (operator) {
        case 'equals':
          return actualValue === value;
        case 'not_equals':
          return actualValue !== value;
        case 'greater_than':
          return Number(actualValue) > Number(value);
        case 'less_than':
          return Number(actualValue) < Number(value);
        case 'contains':
          return String(actualValue).includes(String(value));
        case 'exists':
          return actualValue !== undefined && actualValue !== null;
        default:
          return true;
      }
    } catch (error) {
      this.logger.error('Condition evaluation error:', error);
      return false;
    }
  }

  private replaceVariables(text: string, variables: Record<string, any>): string {
    return text.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
      const value = this.getNestedValue(variables, path);
      return value !== undefined ? String(value) : match;
    });
  }

  private replaceObjectVariables(obj: any, variables: Record<string, any>): any {
    if (typeof obj === 'string') {
      return this.replaceVariables(obj, variables);
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.replaceObjectVariables(item, variables));
    }
    
    if (obj && typeof obj === 'object') {
      const result = {};
      for (const [key, value] of Object.entries(obj)) {
        result[key] = this.replaceObjectVariables(value, variables);
      }
      return result;
    }
    
    return obj;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  async getExecutionStatus(executionId: string) {
    return await this.prisma.workflowExecution.findUnique({
      where: { id: executionId },
      include: {
        nodeExecutions: true,
        agentExecutions: true,
        toolExecutions: true,
      },
    });
  }
}