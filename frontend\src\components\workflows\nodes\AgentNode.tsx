'use client'

import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Bo<PERSON> } from 'lucide-react'

interface AgentNodeData {
  label: string
  agentId: string
  prompt: string
  provider: string
}

export const AgentNode = memo(({ data, selected }: NodeProps<AgentNodeData>) => {
  return (
    <div className={`
      relative bg-white dark:bg-gray-800 border-2 rounded-lg shadow-lg min-w-[200px]
      ${selected ? 'border-blue-500' : 'border-gray-200 dark:border-gray-700'}
      transition-all duration-200 hover:shadow-xl
    `}>
      <Handle type="target" position={Position.Top} className="w-3 h-3" />
      
      <div className="p-4">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <Bot className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
              {data.label}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              AI Agent
            </p>
          </div>
        </div>
        
        {data.prompt && (
          <p className="text-xs text-gray-600 dark:text-gray-300 mt-2 line-clamp-2">
            {data.prompt}
          </p>
        )}
        
        {data.provider && (
          <div className="mt-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
              {data.provider}
            </span>
          </div>
        )}
      </div>
      
      <Handle type="source" position={Position.Bottom} className="w-3 h-3" />
    </div>
  )
})