'use client'

import { motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'

const mcpAgents = [
  { name: 'Intent', color: 'bg-blue-500', desc: 'Classify user intent' },
  { name: 'Retriever', color: 'bg-green-500', desc: 'Search knowledge base' },
  { name: 'Tool', color: 'bg-purple-500', desc: 'Execute actions' },
  { name: 'Workflow', color: 'bg-orange-500', desc: 'Multi-step processes' },
  { name: 'Memory', color: 'bg-red-500', desc: 'Context preservation' },
  { name: 'Follow', color: 'bg-yellow-500', desc: 'Task management' },
  { name: 'Formatter', color: 'bg-indigo-500', desc: 'Response structuring' },
  { name: 'Guardrail', color: 'bg-pink-500', desc: 'Safety & compliance' },
  { name: 'LLM', color: 'bg-cyan-500', desc: 'AI response generation' }
]

export function MCPPipeline() {
  return (
    <section className="py-20 lg:py-32 relative">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-5xl font-bold text-white mb-6"
          >
            MCP Agent
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              {' '}Orchestration Pipeline
            </span>
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto mb-12"
          >
            Every user interaction flows through our sophisticated 9-agent pipeline, 
            ensuring intelligent, contextual, and safe responses.
          </motion.p>
        </div>

        <div className="relative">
          <div className="flex flex-wrap justify-center items-center gap-4 lg:gap-6">
            {mcpAgents.map((agent, index) => (
              <div key={agent.name} className="flex items-center">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="glass-effect rounded-xl p-6 text-center min-w-[140px] hover:scale-105 transition-transform duration-300"
                >
                  <div className={`w-12 h-12 ${agent.color} rounded-full mx-auto mb-3 flex items-center justify-center text-white font-bold text-lg`}>
                    {index + 1}
                  </div>
                  <h3 className="text-white font-semibold mb-2">{agent.name}</h3>
                  <p className="text-gray-300 text-xs">{agent.desc}</p>
                </motion.div>
                
                {index < mcpAgents.length - 1 && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 + 0.2 }}
                    className="hidden lg:block mx-2"
                  >
                    <ArrowRight className="w-6 h-6 text-gray-400" />
                  </motion.div>
                )}
              </div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mt-16 text-center"
          >
            <div className="glass-effect rounded-xl p-8 max-w-4xl mx-auto">
              <h3 className="text-2xl font-bold text-white mb-4">Execution Flow</h3>
              <p className="text-gray-300 text-lg leading-relaxed">
                <span className="text-blue-400">User Input</span> → 
                <span className="text-green-400"> Widget</span> → 
                <span className="text-purple-400"> NestJS API</span> → 
                <span className="text-orange-400"> Agent Pipeline</span> → 
                <span className="text-cyan-400"> Intelligent Response</span>
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}