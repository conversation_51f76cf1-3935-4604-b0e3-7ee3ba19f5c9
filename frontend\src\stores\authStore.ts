import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import axios from 'axios'

interface User {
  id: string
  email: string
  name: string
  role: string
  workspace: any
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  register: (email: string, password: string, name: string, workspaceName?: string) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,

      login: async (email: string, password: string) => {
        try {
          const response = await axios.post('/api/auth/login', {
            email,
            password,
          })

          const { user, accessToken } = response.data

          // Set auth header for future requests
          axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`

          set({
            user,
            token: accessToken,
            isAuthenticated: true,
          })
        } catch (error: any) {
          throw new Error(error.response?.data?.message || 'Login failed')
        }
      },

      register: async (email: string, password: string, name: string, workspaceName?: string) => {
        try {
          const response = await axios.post('/api/auth/register', {
            email,
            password,
            name,
            workspaceName,
          })

          const { user, accessToken } = response.data

          // Set auth header for future requests
          axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`

          set({
            user,
            token: accessToken,
            isAuthenticated: true,
          })
        } catch (error: any) {
          throw new Error(error.response?.data?.message || 'Registration failed')
        }
      },

      logout: () => {
        // Clear auth header
        delete axios.defaults.headers.common['Authorization']

        set({
          user: null,
          token: null,
          isAuthenticated: false,
        })
      },

      refreshToken: async () => {
        try {
          const { token } = get()
          if (!token) throw new Error('No refresh token available')

          const response = await axios.post('/api/auth/refresh', {
            refreshToken: token,
          })

          const { accessToken } = response.data

          // Update auth header
          axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`

          set({
            token: accessToken,
          })
        } catch (error) {
          // If refresh fails, logout
          get().logout()
          throw error
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

// Set up axios interceptor to handle token refresh
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      try {
        await useAuthStore.getState().refreshToken()
        // Retry the original request
        return axios(error.config)
      } catch (refreshError) {
        // Refresh failed, redirect to login
        useAuthStore.getState().logout()
        window.location.href = '/login'
      }
    }
    return Promise.reject(error)
  }
)