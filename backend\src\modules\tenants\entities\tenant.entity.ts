import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm'

@Entity('tenants')
export class Tenant {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ unique: true })
  name: string

  @Column()
  domain: string

  @Column('text', { nullable: true })
  description: string

  @Column('json', { nullable: true })
  settings: Record<string, any>

  @Column('json', { nullable: true })
  businessConfig: Record<string, any>

  @Column('json', { nullable: true })
  modules: string[]

  @Column({ default: true })
  isActive: boolean

  @Column({ nullable: true })
  logo: string

  @Column({ nullable: true })
  primaryColor: string

  @Column({ nullable: true })
  secondaryColor: string

  @Column({ type: 'enum', enum: ['trial', 'basic', 'pro', 'enterprise'], default: 'trial' })
  plan: string

  @Column({ nullable: true })
  subscriptionEndDate: Date

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}