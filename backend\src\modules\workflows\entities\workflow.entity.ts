import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm'

@Entity('workflows')
export class Workflow {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  name: string

  @Column('text', { nullable: true })
  description: string

  @Column()
  tenantId: string

  @Column('json')
  definition: Record<string, any>

  @Column('json', { nullable: true })
  metadata: Record<string, any>

  @Column({ default: true })
  isActive: boolean

  @Column({ nullable: true })
  category: string

  @Column('simple-array', { nullable: true })
  tags: string[]

  @Column({ default: 0 })
  version: number

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}