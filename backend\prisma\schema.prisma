generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String?
  name      String
  avatar    String?
  role      UserRole @default(USER)
  
  workspaceId String?
  workspace   Workspace? @relation(fields: [workspaceId], references: [id])
  
  createdAgents  Agent[]
  createdTools   Tool[]
  createdWorkflows Workflow[]
  
  sessions Session[]
  executions WorkflowExecution[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("users")
}

model Workspace {
  id          String @id @default(cuid())
  name        String
  slug        String @unique
  description String?
  
  plan        Plan @default(FREE)
  settings    Json @default("{}")
  
  owner   User   @relation(fields: [ownerId], references: [id])
  ownerId String
  
  users User[]
  agents Agent[]
  tools  Tool[]
  workflows Workflow[]
  aiProviders AIProvider[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("workspaces")
}

model Agent {
  id          String    @id @default(cuid())
  name        String
  description String?
  type        AgentType @default(CHAT)
  
  // Configuration
  config      J<PERSON> @default("{}")
  personality <PERSON><PERSON> @default("{}")
  
  // AI Provider Settings
  model       String
  temperature Float @default(0.7)
  maxTokens   Int   @default(2000)
  
  // Tools and capabilities
  tools       AgentTool[]
  
  // Status
  isActive    Boolean @default(true)
  isPublic    Boolean @default(false)
  
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId String
  
  creator     User   @relation(fields: [creatorId], references: [id])
  creatorId   String
  
  // Usage in workflows
  workflowNodes WorkflowNode[]
  executions    AgentExecution[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("agents")
}

model Tool {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    ToolCategory @default(CUSTOM)
  
  // Tool configuration
  type        ToolType
  config      Json     // Stores endpoint, auth, etc.
  schema      Json     // Input/output schema
  
  // Access control
  isPublic    Boolean @default(false)
  
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId String
  
  creator     User   @relation(fields: [creatorId], references: [id])
  creatorId   String
  
  // Usage
  agentTools  AgentTool[]
  workflowNodes WorkflowNode[]
  executions  ToolExecution[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("tools")
}

model AgentTool {
  id      String @id @default(cuid())
  
  agent   Agent  @relation(fields: [agentId], references: [id], onDelete: Cascade)
  agentId String
  
  tool    Tool   @relation(fields: [toolId], references: [id], onDelete: Cascade)
  toolId  String
  
  config  Json @default("{}")
  
  @@unique([agentId, toolId])
  @@map("agent_tools")
}

model Workflow {
  id          String @id @default(cuid())
  name        String
  description String?
  
  // Workflow definition
  nodes       WorkflowNode[]
  edges       WorkflowEdge[]
  
  // Triggers
  triggers    WorkflowTrigger[]
  
  // Status
  isActive    Boolean @default(true)
  isPublic    Boolean @default(false)
  version     Int     @default(1)
  
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId String
  
  creator     User   @relation(fields: [creatorId], references: [id])
  creatorId   String
  
  executions  WorkflowExecution[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("workflows")
}

model WorkflowNode {
  id         String @id @default(cuid())
  type       NodeType
  position   Json // {x: number, y: number}
  data       Json // Node-specific configuration
  
  workflow   Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  workflowId String
  
  // Optional relations
  agent      Agent?  @relation(fields: [agentId], references: [id])
  agentId    String?
  
  tool       Tool?   @relation(fields: [toolId], references: [id])
  toolId     String?
  
  // Connections
  sourceEdges WorkflowEdge[] @relation("SourceNode")
  targetEdges WorkflowEdge[] @relation("TargetNode")
  
  @@map("workflow_nodes")
}

model WorkflowEdge {
  id            String @id @default(cuid())
  
  sourceNode    WorkflowNode @relation("SourceNode", fields: [sourceNodeId], references: [id], onDelete: Cascade)
  sourceNodeId  String
  sourceHandle  String?
  
  targetNode    WorkflowNode @relation("TargetNode", fields: [targetNodeId], references: [id], onDelete: Cascade)
  targetNodeId  String
  targetHandle  String?
  
  // Conditional logic
  condition     Json?
  
  workflow      Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  workflowId    String
  
  @@map("workflow_edges")
}

model WorkflowTrigger {
  id         String      @id @default(cuid())
  type       TriggerType
  config     Json        // Trigger-specific configuration
  
  workflow   Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  workflowId String
  
  @@map("workflow_triggers")
}

model WorkflowExecution {
  id          String            @id @default(cuid())
  status      ExecutionStatus   @default(PENDING)
  
  // Input data
  input       Json
  context     Json @default("{}")
  
  // Results
  output      Json?
  error       String?
  
  // Timing
  startedAt   DateTime @default(now())
  completedAt DateTime?
  
  workflow    Workflow @relation(fields: [workflowId], references: [id])
  workflowId  String
  
  triggeredBy User?    @relation(fields: [userId], references: [id])
  userId      String?
  
  // Node executions
  nodeExecutions NodeExecution[]
  agentExecutions AgentExecution[]
  toolExecutions ToolExecution[]
  
  @@map("workflow_executions")
}

model NodeExecution {
  id          String          @id @default(cuid())
  nodeId      String
  status      ExecutionStatus @default(PENDING)
  
  input       Json
  output      Json?
  error       String?
  
  startedAt   DateTime @default(now())
  completedAt DateTime?
  
  execution   WorkflowExecution @relation(fields: [executionId], references: [id], onDelete: Cascade)
  executionId String
  
  @@map("node_executions")
}

model AgentExecution {
  id          String          @id @default(cuid())
  status      ExecutionStatus @default(PENDING)
  
  prompt      String
  response    String?
  metadata    Json @default("{}")
  
  tokensUsed  Int?
  
  startedAt   DateTime @default(now())
  completedAt DateTime?
  
  agent       Agent @relation(fields: [agentId], references: [id])
  agentId     String
  
  execution   WorkflowExecution? @relation(fields: [executionId], references: [id])
  executionId String?
  
  @@map("agent_executions")
}

model ToolExecution {
  id          String          @id @default(cuid())
  status      ExecutionStatus @default(PENDING)
  
  input       Json
  output      Json?
  error       String?
  
  startedAt   DateTime @default(now())
  completedAt DateTime?
  
  tool        Tool @relation(fields: [toolId], references: [id])
  toolId      String
  
  execution   WorkflowExecution? @relation(fields: [executionId], references: [id])
  executionId String?
  
  @@map("tool_executions")
}

model AIProvider {
  id          String @id @default(cuid())
  name        String
  type        ProviderType
  
  // Configuration
  config      Json // API keys, endpoints, etc.
  isActive    Boolean @default(true)
  
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId String
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("ai_providers")
}

model Session {
  id          String @id @default(cuid())
  sessionId   String @unique
  
  // Context and state
  context     Json @default("{}")
  metadata    Json @default("{}")
  
  // User association
  user        User?   @relation(fields: [userId], references: [id])
  userId      String?
  
  isActive    Boolean @default(true)
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  expiresAt   DateTime
  
  @@map("sessions")
}

// Enums
enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

enum Plan {
  FREE
  PRO
  ENTERPRISE
}

enum AgentType {
  CHAT
  TASK
  EXTRACTOR
  ANALYST
  CONNECTOR
  EVALUATOR
}

enum ToolType {
  REST_API
  GRAPHQL
  PYTHON_SCRIPT
  BROWSER_ACTION
  WEBHOOK
  DATABASE
}

enum ToolCategory {
  BUSINESS
  AI
  DEVOPS
  CRM
  COMMUNICATION
  FINANCE
  PRODUCTIVITY
  ANALYTICS
  WEB_SCRAPING
  CUSTOM
}

enum NodeType {
  START
  END
  AGENT
  TOOL
  CONDITION
  DELAY
  APPROVAL
  WEBHOOK
  TRIGGER
}

enum TriggerType {
  MANUAL
  WEBHOOK
  SCHEDULE
  EVENT
}

enum ExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

enum ProviderType {
  OPENAI
  ANTHROPIC
  GOOGLE
  MISTRAL
  HUGGINGFACE
  COHERE
  GROQ
  CUSTOM
}