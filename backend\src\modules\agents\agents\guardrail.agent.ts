import { Injectable } from '@nestjs/common'

export interface SafetyCheck {
  safe: boolean
  reason?: string
  violations: string[]
  riskLevel: 'low' | 'medium' | 'high'
  recommendations: string[]
}

@Injectable()
export class GuardrailAgent {
  private prohibitedPatterns = [
    // Personal information patterns
    /\b\d{3}-\d{2}-\d{4}\b/, // SSN
    /\b\d{16}\b/, // Credit card
    /password\s*[:=]\s*\S+/i,
    
    // Malicious content
    /\b(hack|exploit|vulnerability|malware)\b/i,
    
    // Inappropriate content
    /\b(hate|violence|discrimination)\b/i,
    
    // System commands
    /\b(rm -rf|delete|drop table|truncate)\b/i,
  ]

  private businessRules = [
    {
      name: 'data_access_limit',
      description: 'Limit data access to authorized users',
      validator: (context: any) => context.userId && context.tenantId,
    },
    {
      name: 'workflow_execution_limit',
      description: 'Limit workflow execution to business hours',
      validator: (context: any) => {
        const hour = new Date().getHours()
        return hour >= 6 && hour <= 22 // 6 AM to 10 PM
      },
    },
    {
      name: 'rate_limiting',
      description: 'Prevent excessive API calls',
      validator: (context: any) => true, // Simplified for demo
    },
  ]

  async validateRequest(prompt: any, context: any): Promise<SafetyCheck> {
    const violations: string[] = []
    let riskLevel: 'low' | 'medium' | 'high' = 'low'

    // Check for prohibited patterns
    const contentViolations = this.checkContentSafety(prompt.user)
    violations.push(...contentViolations)

    // Check business rules
    const businessViolations = this.checkBusinessRules(context)
    violations.push(...businessViolations)

    // Check data access permissions
    const permissionViolations = this.checkPermissions(prompt, context)
    violations.push(...permissionViolations)

    // Determine risk level
    if (violations.length === 0) {
      riskLevel = 'low'
    } else if (violations.length <= 2) {
      riskLevel = 'medium'
    } else {
      riskLevel = 'high'
    }

    const safe = violations.length === 0 || riskLevel === 'low'

    return {
      safe,
      reason: violations.length > 0 ? violations[0] : undefined,
      violations,
      riskLevel,
      recommendations: this.generateRecommendations(violations),
    }
  }

  private checkContentSafety(content: string): string[] {
    const violations: string[] = []

    for (const pattern of this.prohibitedPatterns) {
      if (pattern.test(content)) {
        violations.push(`Prohibited content pattern detected: ${pattern.source}`)
      }
    }

    // Check for potential injection attacks
    if (content.includes('<script>') || content.includes('javascript:')) {
      violations.push('Potential script injection detected')
    }

    // Check for SQL injection patterns
    if (/(\bor\b|\band\b).*['"]\s*=\s*['"]/.test(content.toLowerCase())) {
      violations.push('Potential SQL injection pattern detected')
    }

    return violations
  }

  private checkBusinessRules(context: any): string[] {
    const violations: string[] = []

    for (const rule of this.businessRules) {
      try {
        if (!rule.validator(context)) {
          violations.push(`Business rule violation: ${rule.name} - ${rule.description}`)
        }
      } catch (error) {
        violations.push(`Business rule validation error: ${rule.name}`)
      }
    }

    return violations
  }

  private checkPermissions(prompt: any, context: any): string[] {
    const violations: string[] = []

    // Check tenant isolation
    if (!context.tenantId) {
      violations.push('Missing tenant context - request blocked for security')
    }

    // Check user authentication
    if (!context.userId) {
      violations.push('Missing user authentication - request blocked')
    }

    // Check for admin-only operations
    const adminOnlyPatterns = [
      /delete.*user/i,
      /modify.*permission/i,
      /system.*config/i,
    ]

    for (const pattern of adminOnlyPatterns) {
      if (pattern.test(prompt.user) && context.role !== 'tenant_admin' && context.role !== 'super_admin') {
        violations.push('Admin privileges required for this operation')
      }
    }

    return violations
  }

  private generateRecommendations(violations: string[]): string[] {
    const recommendations: string[] = []

    if (violations.some(v => v.includes('injection'))) {
      recommendations.push('Review input sanitization and validation')
    }

    if (violations.some(v => v.includes('authentication'))) {
      recommendations.push('Ensure proper user authentication before proceeding')
    }

    if (violations.some(v => v.includes('permission'))) {
      recommendations.push('Verify user permissions and role-based access controls')
    }

    if (violations.some(v => v.includes('business rule'))) {
      recommendations.push('Review business rule configuration and compliance')
    }

    if (recommendations.length === 0 && violations.length > 0) {
      recommendations.push('Review request content and try again with appropriate modifications')
    }

    return recommendations
  }

  async logSecurityEvent(event: any): Promise<void> {
    // Log security events for audit trail
    console.log('Security Event:', {
      timestamp: new Date().toISOString(),
      tenantId: event.tenantId,
      userId: event.userId,
      sessionId: event.sessionId,
      eventType: event.type,
      severity: event.severity,
      details: event.details,
    })
  }
}