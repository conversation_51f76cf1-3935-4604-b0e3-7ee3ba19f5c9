import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards, 
  Request,
  Query,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AgentsService } from './agents.service';
import { CreateAgentDto, UpdateAgentDto, TestAgentDto } from './dto/agent.dto';

@Controller('agents')
@UseGuards(AuthGuard('jwt'))
export class AgentsController {
  constructor(private readonly agentsService: AgentsService) {}

  @Post()
  create(@Body() createAgentDto: CreateAgentDto, @Request() req) {
    return this.agentsService.create(createAgentDto, req.user.id);
  }

  @Get()
  findAll(@Request() req, @Query('public') isPublic?: string) {
    const publicOnly = isPublic === 'true' ? true : undefined;
    return this.agentsService.findAll(req.user.workspaceId, publicOnly);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.agentsService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string, 
    @Body() updateAgentDto: UpdateAgentDto,
    @Request() req
  ) {
    return this.agentsService.update(id, updateAgentDto, req.user.id);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.agentsService.remove(id, req.user.id);
  }

  @Post(':id/test')
  testAgent(
    @Param('id') id: string,
    @Body() testAgentDto: TestAgentDto,
    @Request() req
  ) {
    return this.agentsService.testAgent(id, testAgentDto.prompt, req.user.id);
  }

  @Get(':id/metrics')
  getMetrics(
    @Param('id') id: string,
    @Query('timeRange') timeRange?: string
  ) {
    return this.agentsService.getAgentMetrics(id, timeRange);
  }
}