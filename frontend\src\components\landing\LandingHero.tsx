'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { ArrowRight, Bot, Building2, Zap } from 'lucide-react'
import Link from 'next/link'
import { motion } from 'framer-motion'

export function LandingHero() {
  return (
    <section className="relative overflow-hidden py-20 lg:py-32">
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20" />
      
      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 text-sm text-white mb-6">
              <Zap className="w-4 h-4" />
              <span>Powered by Advanced MCP Agent Orchestration</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6">
              Build Complete
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                {' '}Business Systems
              </span>
              <br />
              with AI Interviews
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              AxientOS transforms business requirements into fully operational systems through AI-powered interviews, 
              automatic module generation, and intelligent agent orchestration.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
          >
            <Link href="/onboarding">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg">
                Start AI Interview
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </Link>
            
            <Link href="/demo">
              <Button variant="outline" size="lg" className="border-white/20 text-white hover:bg-white/10 px-8 py-4 text-lg">
                Watch Demo
              </Button>
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
          >
            <div className="glass-effect rounded-xl p-6 text-center">
              <Bot className="w-12 h-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">9 MCP Agents</h3>
              <p className="text-gray-300">Intent, Retriever, Tool, Workflow, Memory, Follow, Formatter, Guardrail & LLM</p>
            </div>
            
            <div className="glass-effect rounded-xl p-6 text-center">
              <Building2 className="w-12 h-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Auto-Generated Modules</h3>
              <p className="text-gray-300">CRM, HR, Finance, Orders, Support, Legal & Custom modules built automatically</p>
            </div>
            
            <div className="glass-effect rounded-xl p-6 text-center">
              <Zap className="w-12 h-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Real-time Orchestration</h3>
              <p className="text-gray-300">WebSocket-powered agent coordination with session persistence</p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}