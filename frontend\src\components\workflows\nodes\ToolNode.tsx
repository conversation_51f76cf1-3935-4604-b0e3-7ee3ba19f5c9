'use client'

import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Wrench } from 'lucide-react'

interface ToolNodeData {
  label: string
  toolId: string
  input: Record<string, any>
}

export const ToolNode = memo(({ data, selected }: NodeProps<ToolNodeData>) => {
  return (
    <div className={`
      relative bg-white dark:bg-gray-800 border-2 rounded-lg shadow-lg min-w-[200px]
      ${selected ? 'border-orange-500' : 'border-gray-200 dark:border-gray-700'}
      transition-all duration-200 hover:shadow-xl
    `}>
      <Handle type="target" position={Position.Top} className="w-3 h-3" />
      
      <div className="p-4">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
            <Wrench className="w-4 h-4 text-orange-600 dark:text-orange-400" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
              {data.label}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Tool
            </p>
          </div>
        </div>
        
        {data.input && Object.keys(data.input).length > 0 && (
          <div className="mt-2">
            <p className="text-xs text-gray-600 dark:text-gray-300">
              {Object.keys(data.input).length} parameter(s)
            </p>
          </div>
        )}
      </div>
      
      <Handle type="source" position={Position.Bottom} className="w-3 h-3" />
    </div>
  )
})