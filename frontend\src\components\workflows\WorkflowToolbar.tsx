'use client'

import { useState } from 'react'
import { 
  Save, 
  Play, 
  Eye, 
  Undo, 
  Redo, 
  Plus,
  Settings,
  Download,
  Upload,
  Copy,
  Trash2,
  MoreHorizontal
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { 
  Sheet, 
  SheetContent, 
  SheetDescription, 
  SheetHeader, 
  SheetTitle 
} from '@/components/ui/sheet'
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from '@/components/ui/tooltip'
import { Badge } from '@/components/ui/badge'
import { NodeTemplateSelector } from './NodeTemplateSelector'
import { WorkflowSettings } from './WorkflowSettings'
import { toast } from 'react-hot-toast'

interface WorkflowToolbarProps {
  onSave?: () => void
  onExecute?: () => void
  onPreview?: () => void
  onUndo?: () => void
  onRedo?: () => void
  onAddNode?: (nodeType: string) => void
  canUndo?: boolean
  canRedo?: boolean
  canExecute?: boolean
  isDirty?: boolean
  isExecuting?: boolean
  workflow?: any
}

export function WorkflowToolbar({
  onSave,
  onExecute,
  onPreview,
  onUndo,
  onRedo,
  onAddNode,
  canUndo = false,
  canRedo = false,
  canExecute = false,
  isDirty = false,
  isExecuting = false,
  workflow
}: WorkflowToolbarProps) {
  const [showNodeSelector, setShowNodeSelector] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  const handleSave = () => {
    onSave?.()
    toast.success('Workflow saved successfully')
  }

  const handleExecute = () => {
    if (!canExecute) {
      toast.error('Workflow must be saved before execution')
      return
    }
    onExecute?.()
    toast.success('Workflow execution started')
  }

  const handleExport = () => {
    if (!workflow) return
    
    const data = JSON.stringify(workflow, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${workflow.name || 'workflow'}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Workflow exported')
  }

  const handleImport = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return
      
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target?.result as string)
          // Handle import logic here
          toast.success('Workflow imported successfully')
        } catch (error) {
          toast.error('Invalid workflow file')
        }
      }
      reader.readAsText(file)
    }
    input.click()
  }

  const handleClone = () => {
    // Clone workflow logic
    toast.success('Workflow cloned')
  }

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this workflow?')) {
      // Delete workflow logic
      toast.success('Workflow deleted')
    }
  }

  return (
    <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      <div className="flex items-center gap-2">
        <TooltipProvider>
          {/* Save Button */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSave}
                disabled={!isDirty}
                className="relative"
              >
                <Save className="w-4 h-4" />
                {isDirty && (
                  <Badge variant="destructive" className="absolute -top-1 -right-1 w-2 h-2 p-0" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Save workflow {isDirty ? '(unsaved changes)' : ''}</p>
            </TooltipContent>
          </Tooltip>

          {/* Undo/Redo */}
          <div className="flex items-center border rounded-md">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onUndo}
                  disabled={!canUndo}
                  className="rounded-r-none"
                >
                  <Undo className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Undo</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRedo}
                  disabled={!canRedo}
                  className="rounded-l-none border-l"
                >
                  <Redo className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Redo</p>
              </TooltipContent>
            </Tooltip>
          </div>

          {/* Add Node */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowNodeSelector(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Node
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Add a new node to the workflow</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Workflow Info */}
      <div className="flex items-center gap-4">
        {workflow?.name && (
          <div className="text-sm">
            <span className="font-medium">{workflow.name}</span>
            {workflow.version && (
              <Badge variant="secondary" className="ml-2">
                v{workflow.version}
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowPreview(true)}
                disabled={!workflow}
              >
                <Eye className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Preview workflow</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                onClick={handleExecute}
                disabled={!canExecute || isExecuting}
                className="bg-green-600 hover:bg-green-700"
              >
                <Play className="w-4 h-4 mr-2" />
                {isExecuting ? 'Running...' : 'Run'}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Execute workflow</p>
            </TooltipContent>
          </Tooltip>

          {/* More Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setShowSettings(true)}>
                <Settings className="w-4 h-4 mr-2" />
                Workflow Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleExport}>
                <Download className="w-4 h-4 mr-2" />
                Export
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleImport}>
                <Upload className="w-4 h-4 mr-2" />
                Import
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleClone}>
                <Copy className="w-4 h-4 mr-2" />
                Clone
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={handleDelete}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TooltipProvider>
      </div>

      {/* Node Template Selector */}
      <Sheet open={showNodeSelector} onOpenChange={setShowNodeSelector}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Add Node</SheetTitle>
            <SheetDescription>
              Choose a node type to add to your workflow
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <NodeTemplateSelector
              onSelect={(nodeType) => {
                onAddNode?.(nodeType)
                setShowNodeSelector(false)
              }}
            />
          </div>
        </SheetContent>
      </Sheet>

      {/* Workflow Settings */}
      <Sheet open={showSettings} onOpenChange={setShowSettings}>
        <SheetContent className="w-[400px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle>Workflow Settings</SheetTitle>
            <SheetDescription>
              Configure workflow properties and behavior
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <WorkflowSettings 
              workflow={workflow}
              onSave={(settings) => {
                // Handle settings save
                setShowSettings(false)
                toast.success('Settings saved')
              }}
            />
          </div>
        </SheetContent>
      </Sheet>

      {/* Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Workflow Preview</DialogTitle>
            <DialogDescription>
              Review your workflow structure and configuration
            </DialogDescription>
          </DialogHeader>
          <div className="mt-6">
            {/* Workflow preview content */}
            <div className="text-center py-8 text-gray-500">
              <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Workflow preview coming soon</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}