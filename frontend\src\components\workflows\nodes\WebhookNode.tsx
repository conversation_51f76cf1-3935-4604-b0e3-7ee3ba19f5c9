'use client'

import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Webhook } from 'lucide-react'

interface WebhookNodeData {
  label: string
  url: string
  method: string
  headers: Record<string, string>
  body: Record<string, any>
}

export const WebhookNode = memo(({ data, selected }: NodeProps<WebhookNodeData>) => {
  return (
    <div className={`
      relative bg-white dark:bg-gray-800 border-2 rounded-lg shadow-lg min-w-[200px]
      ${selected ? 'border-indigo-500' : 'border-gray-200 dark:border-gray-700'}
      transition-all duration-200 hover:shadow-xl
    `}>
      <Handle type="target" position={Position.Top} className="w-3 h-3" />
      
      <div className="p-4">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded-lg">
            <Webhook className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
              {data.label}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Webhook
            </p>
          </div>
        </div>
        
        {data.method && (
          <div className="mt-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200">
              {data.method}
            </span>
          </div>
        )}
        
        {data.url && (
          <p className="text-xs text-gray-600 dark:text-gray-300 mt-2 truncate">
            {data.url}
          </p>
        )}
      </div>
      
      <Handle type="source" position={Position.Bottom} className="w-3 h-3" />
    </div>
  )
})