'use client'

import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Play } from 'lucide-react'

interface StartNodeData {
  label: string
}

export const StartNode = memo(({ data, selected }: NodeProps<StartNodeData>) => {
  return (
    <div className={`
      relative bg-white dark:bg-gray-800 border-2 rounded-full shadow-lg w-16 h-16 flex items-center justify-center
      ${selected ? 'border-green-500' : 'border-gray-200 dark:border-gray-700'}
      transition-all duration-200 hover:shadow-xl
    `}>
      <div className="p-2 bg-green-100 dark:bg-green-900 rounded-full">
        <Play className="w-6 h-6 text-green-600 dark:text-green-400" />
      </div>
      
      <Handle type="source" position={Position.Bottom} className="w-3 h-3" />
    </div>
  )
})