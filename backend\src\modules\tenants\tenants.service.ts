import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { Tenant } from './entities/tenant.entity'

@Injectable()
export class TenantsService {
  constructor(
    @InjectRepository(Tenant)
    private tenantsRepository: Repository<Tenant>,
  ) {}

  async create(createTenantDto: any): Promise<Tenant> {
    const tenant = this.tenantsRepository.create(createTenantDto)
    return this.tenantsRepository.save(tenant)
  }

  async findAll(): Promise<Tenant[]> {
    return this.tenantsRepository.find()
  }

  async findOne(id: string): Promise<Tenant> {
    return this.tenantsRepository.findOne({ where: { id } })
  }

  async update(id: string, updateTenantDto: any): Promise<Tenant> {
    await this.tenantsRepository.update(id, updateTenantDto)
    return this.findOne(id)
  }

  async remove(id: string): Promise<void> {
    await this.tenantsRepository.delete(id)
  }
}