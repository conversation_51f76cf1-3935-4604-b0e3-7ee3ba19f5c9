import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards, 
  Request,
  Query,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ToolsService } from './tools.service';
import { CreateToolDto, UpdateToolDto, TestToolDto } from './dto/tool.dto';

@Controller('tools')
@UseGuards(AuthGuard('jwt'))
export class ToolsController {
  constructor(private readonly toolsService: ToolsService) {}

  @Post()
  create(@Body() createToolDto: CreateToolDto, @Request() req) {
    return this.toolsService.create(createToolDto, req.user.id);
  }

  @Get()
  findAll(
    @Request() req, 
    @Query('category') category?: string,
    @Query('public') isPublic?: string
  ) {
    const publicOnly = isPublic === 'true' ? true : undefined;
    return this.toolsService.findAll(req.user.workspaceId, category, publicOnly);
  }

  @Get('categories')
  getCategories() {
    return this.toolsService.getCategories();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.toolsService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string, 
    @Body() updateToolDto: UpdateToolDto,
    @Request() req
  ) {
    return this.toolsService.update(id, updateToolDto, req.user.id);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.toolsService.remove(id, req.user.id);
  }

  @Post(':id/test')
  testTool(
    @Param('id') id: string,
    @Body() testToolDto: TestToolDto,
    @Request() req
  ) {
    return this.toolsService.testTool(id, testToolDto.input, req.user.id);
  }

  @Get(':id/metrics')
  getMetrics(
    @Param('id') id: string,
    @Query('timeRange') timeRange?: string
  ) {
    return this.toolsService.getToolMetrics(id, timeRange);
  }

  @Post('validate-schema')
  validateSchema(@Body() schema: any) {
    return this.toolsService.validateSchema(schema);
  }
}