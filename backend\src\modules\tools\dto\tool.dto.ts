import { IsString, IsOptional, IsBoolean, IsObject, IsEnum } from 'class-validator';

export enum ToolType {
  REST_API = 'REST_API',
  GRAPHQL = 'GRAPHQL',
  PYTHON_SCRIPT = 'PYTHON_SCRIPT',
  BROWSER_ACTION = 'BROWSER_ACTION',
  WEBHOOK = 'WEBHOOK',
  DATABASE = 'DATABASE',
}

export enum ToolCategory {
  BUSINESS = 'BUSINESS',
  AI = 'AI',
  DEVOPS = 'DEVOPS',
  CRM = 'CRM',
  COMMUNICATION = 'COMMUNICATION',
  FINANCE = 'FINANCE',
  PRODUCTIVITY = 'PRODUCTIVITY',
  ANALYTICS = 'ANALYTICS',
  WEB_SCRAPING = 'WEB_SCRAPING',
  CUSTOM = 'CUSTOM',
}

export class CreateToolDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(ToolType)
  type: ToolType;

  @IsEnum(ToolCategory)
  category: ToolCategory;

  @IsObject()
  config: Record<string, any>;

  @IsOptional()
  @IsObject()
  schema?: Record<string, any>;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;
}

export class UpdateToolDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(ToolType)
  type?: ToolType;

  @IsOptional()
  @IsEnum(ToolCategory)
  category?: ToolCategory;

  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @IsOptional()
  @IsObject()
  schema?: Record<string, any>;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;
}

export class TestToolDto {
  @IsObject()
  input: Record<string, any>;
}