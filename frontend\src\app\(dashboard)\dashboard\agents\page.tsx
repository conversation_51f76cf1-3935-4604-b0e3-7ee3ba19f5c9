'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Bot, 
  Play, 
  Edit, 
  Trash2, 
  <PERSON><PERSON>,
  Eye
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { AgentTestDialog } from '@/components/agents/AgentTestDialog'
import { AgentMetricsDialog } from '@/components/agents/AgentMetricsDialog'
import { apiClient } from '@/lib/api-client'
import { formatDistanceToNow } from 'date-fns'
import toast from 'react-hot-toast'

interface Agent {
  id: string
  name: string
  description: string
  type: string
  model: string
  isActive: boolean
  isPublic: boolean
  creator: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  tools: any[]
  executions: any[]
  _count: {
    executions: number
  }
  createdAt: string
  updatedAt: string
}

const agentTypes = [
  { value: 'all', label: 'All Types' },
  { value: 'CHAT', label: 'Chat' },
  { value: 'TASK', label: 'Task' },
  { value: 'EXTRACTOR', label: 'Extractor' },
  { value: 'ANALYST', label: 'Analyst' },
  { value: 'CONNECTOR', label: 'Connector' },
  { value: 'EVALUATOR', label: 'Evaluator' },
]

const statusFilters = [
  { value: 'all', label: 'All Status' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'public', label: 'Public' },
  { value: 'private', label: 'Private' },
]

export default function AgentsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [testingAgent, setTestingAgent] = useState<Agent | null>(null)
  const [viewingMetrics, setViewingMetrics] = useState<Agent | null>(null)
  const router = useRouter()

  const { data: agents, isLoading, refetch } = useQuery({
    queryKey: ['agents', typeFilter, statusFilter],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (typeFilter !== 'all') params.append('type', typeFilter)
      if (statusFilter === 'public') params.append('public', 'true')
      
      const response = await apiClient.get(`/api/agents?${params.toString()}`)
      return response.data
    }
  })

  const filteredAgents = agents?.filter((agent: Agent) => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && agent.isActive) ||
                         (statusFilter === 'inactive' && !agent.isActive) ||
                         (statusFilter === 'public' && agent.isPublic) ||
                         (statusFilter === 'private' && !agent.isPublic)

    return matchesSearch && matchesStatus
  })

  const handleDeleteAgent = async (agentId: string) => {
    if (confirm('Are you sure you want to delete this agent?')) {
      try {
        await apiClient.delete(`/api/agents/${agentId}`)
        toast.success('Agent deleted successfully')
        refetch()
      } catch (error) {
        toast.error('Failed to delete agent')
      }
    }
  }

  const handleCloneAgent = async (agent: Agent) => {
    try {
      const cloneData = {
        ...agent,
        name: `${agent.name} (Copy)`,
        id: undefined,
        createdAt: undefined,
        updatedAt: undefined,
      }
      await apiClient.post('/api/agents', cloneData)
      toast.success('Agent cloned successfully')
      refetch()
    } catch (error) {
      toast.error('Failed to clone agent')
    }
  }

  const getAgentTypeColor = (type: string) => {
    const colors = {
      CHAT: 'bg-blue-100 text-blue-800',
      TASK: 'bg-green-100 text-green-800',
      EXTRACTOR: 'bg-purple-100 text-purple-800',
      ANALYST: 'bg-orange-100 text-orange-800',
      CONNECTOR: 'bg-indigo-100 text-indigo-800',
      EVALUATOR: 'bg-pink-100 text-pink-800',
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
  }

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Agents</h1>
          <Button disabled>
            <Plus className="w-4 h-4 mr-2" />
            Create Agent
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-3/4" />
                <div className="h-4 bg-gray-200 rounded w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded" />
                  <div className="h-4 bg-gray-200 rounded w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Agents</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your AI agents and their configurations
          </p>
        </div>
        <Button onClick={() => router.push('/dashboard/agents/new')}>
          <Plus className="w-4 h-4 mr-2" />
          Create Agent
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search agents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-40">
            <Filter className="w-4 h-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {agentTypes.map((type) => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {statusFilters.map((status) => (
              <SelectItem key={status.value} value={status.value}>
                {status.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Agents Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAgents?.map((agent: Agent) => (
          <Card key={agent.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <Bot className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate">{agent.name}</CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={getAgentTypeColor(agent.type)}>
                        {agent.type}
                      </Badge>
                      {agent.isActive ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="secondary">Inactive</Badge>
                      )}
                    </div>
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setTestingAgent(agent)}>
                      <Play className="w-4 h-4 mr-2" />
                      Test Agent
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setViewingMetrics(agent)}>
                      <Eye className="w-4 h-4 mr-2" />
                      View Metrics
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => router.push(`/dashboard/agents/${agent.id}`)}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleCloneAgent(agent)}>
                      <Copy className="w-4 h-4 mr-2" />
                      Clone
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => handleDeleteAgent(agent.id)}
                      className="text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <CardDescription className="line-clamp-2">
                {agent.description || 'No description provided'}
              </CardDescription>

              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Model:</span>
                  <span className="font-medium">{agent.model}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Tools:</span>
                  <span className="font-medium">{agent.tools?.length || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Executions:</span>
                  <span className="font-medium">{agent._count?.executions || 0}</span>
                </div>
              </div>

              <div className="flex items-center justify-between pt-3 border-t">
                <div className="flex items-center gap-2">
                  <Avatar className="w-6 h-6">
                    <AvatarImage src={agent.creator.avatar} />
                    <AvatarFallback className="text-xs">
                      {getUserInitials(agent.creator.name)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {agent.creator.name}
                  </span>
                </div>
                <span className="text-xs text-gray-500">
                  {formatDistanceToNow(new Date(agent.createdAt), { addSuffix: true })}
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredAgents?.length === 0 && (
        <div className="text-center py-12">
          <Bot className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No agents found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {searchTerm || typeFilter !== 'all' || statusFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Get started by creating your first AI agent'
            }
          </p>
          <Button onClick={() => router.push('/dashboard/agents/new')}>
            <Plus className="w-4 h-4 mr-2" />
            Create Agent
          </Button>
        </div>
      )}

      {/* Test Agent Dialog */}
      {testingAgent && (
        <AgentTestDialog
          agent={testingAgent}
          open={!!testingAgent}
          onOpenChange={(open) => !open && setTestingAgent(null)}
        />
      )}

      {/* Agent Metrics Dialog */}
      {viewingMetrics && (
        <AgentMetricsDialog
          agent={viewingMetrics}
          open={!!viewingMetrics}
          onOpenChange={(open) => !open && setViewingMetrics(null)}
        />
      )}
    </div>
  )
}