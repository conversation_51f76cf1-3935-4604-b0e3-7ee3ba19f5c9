'use client'

import { use<PERSON><PERSON>back, useState, useRef } from 'react'
import React<PERSON><PERSON>, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
  NodeTypes,
  EdgeTypes,
  Connection,
  ReactFlowProvider,
  useReactFlow,
} from 'reactflow'
import 'reactflow/dist/style.css'

import { WorkflowSidebar } from './WorkflowSidebar'
import { WorkflowToolbar } from './WorkflowToolbar'
import { AgentNode } from './nodes/AgentNode'
import { ToolNode } from './nodes/ToolNode'
import { ConditionNode } from './nodes/ConditionNode'
import { StartNode } from './nodes/StartNode'
import { EndNode } from './nodes/EndNode'
import { DelayNode } from './nodes/DelayNode'
import { WebhookNode } from './nodes/WebhookNode'
import { CustomEdge } from './edges/CustomEdge'
import { NodePropertiesPanel } from './NodePropertiesPanel'

const nodeTypes: NodeTypes = {
  start: StartNode,
  end: EndNode,
  agent: AgentNode,
  tool: ToolNode,
  condition: ConditionNode,
  delay: DelayNode,
  webhook: WebhookNode,
}

const edgeTypes: EdgeTypes = {
  custom: CustomEdge,
}

interface WorkflowBuilderProps {
  workflowId?: string
  initialNodes?: Node[]
  initialEdges?: Edge[]
  onSave?: (nodes: Node[], edges: Edge[]) => void
}

function WorkflowBuilderContent({
  workflowId,
  initialNodes = [],
  initialEdges = [],
  onSave,
}: WorkflowBuilderProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)
  const [isPropertiesPanelOpen, setIsPropertiesPanelOpen] = useState(false)
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const { project } = useReactFlow()

  const onConnect = useCallback(
    (connection: Connection) => {
      const edge = {
        ...connection,
        id: `edge-${Date.now()}`,
        type: 'custom',
        animated: true,
      }
      setEdges((eds) => addEdge(edge, eds))
    },
    [setEdges]
  )

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
  }, [])

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault()

      if (!reactFlowWrapper.current) return

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect()
      const nodeType = event.dataTransfer.getData('application/reactflow')

      if (!nodeType) return

      const position = project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      })

      const newNode: Node = {
        id: `${nodeType}-${Date.now()}`,
        type: nodeType,
        position,
        data: getDefaultNodeData(nodeType),
      }

      setNodes((nds) => nds.concat(newNode))
    },
    [project, setNodes]
  )

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node)
    setIsPropertiesPanelOpen(true)
  }, [])

  const onNodeUpdate = useCallback((nodeId: string, data: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId ? { ...node, data: { ...node.data, ...data } } : node
      )
    )
  }, [setNodes])

  const onSaveWorkflow = useCallback(() => {
    if (onSave) {
      onSave(nodes, edges)
    }
  }, [nodes, edges, onSave])

  const onExecuteWorkflow = useCallback(async () => {
    if (!workflowId) {
      console.error('No workflow ID provided')
      return
    }

    try {
      // Execute workflow via API
      const response = await fetch(`/api/workflows/${workflowId}/execute`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ input: {} }),
      })

      if (!response.ok) {
        throw new Error('Failed to execute workflow')
      }

      const result = await response.json()
      console.log('Workflow execution started:', result)
    } catch (error) {
      console.error('Failed to execute workflow:', error)
    }
  }, [workflowId])

  return (
    <div className="flex h-full">
      <WorkflowSidebar />
      
      <div className="flex-1 flex flex-col">
        <WorkflowToolbar
          onSave={onSaveWorkflow}
          onExecute={onExecuteWorkflow}
          canExecute={!!workflowId && nodes.length > 0}
        />
        
        <div className="flex-1 relative" ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            onDrop={onDrop}
            onDragOver={onDragOver}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            fitView
            attributionPosition="bottom-left"
            className="bg-gray-50 dark:bg-gray-900"
          >
            <Background variant={BackgroundVariant.Dots} />
            <Controls />
            <MiniMap />
          </ReactFlow>
        </div>
      </div>

      {isPropertiesPanelOpen && selectedNode && (
        <NodePropertiesPanel
          node={selectedNode}
          onUpdate={onNodeUpdate}
          onClose={() => setIsPropertiesPanelOpen(false)}
        />
      )}
    </div>
  )
}

function getDefaultNodeData(nodeType: string) {
  switch (nodeType) {
    case 'start':
      return { label: 'Start' }
    case 'end':
      return { label: 'End' }
    case 'agent':
      return { 
        label: 'AI Agent',
        agentId: '',
        prompt: 'Process the input data',
        provider: 'openai',
      }
    case 'tool':
      return { 
        label: 'Tool',
        toolId: '',
        input: {},
      }
    case 'condition':
      return { 
        label: 'Condition',
        condition: {
          variable: '',
          operator: 'equals',
          value: '',
        },
      }
    case 'delay':
      return { 
        label: 'Delay',
        delay: 1000,
      }
    case 'webhook':
      return { 
        label: 'Webhook',
        url: '',
        method: 'POST',
        headers: {},
        body: {},
      }
    default:
      return { label: 'Node' }
  }
}

export function WorkflowBuilder(props: WorkflowBuilderProps) {
  return (
    <ReactFlowProvider>
      <WorkflowBuilderContent {...props} />
    </ReactFlowProvider>
  )
}