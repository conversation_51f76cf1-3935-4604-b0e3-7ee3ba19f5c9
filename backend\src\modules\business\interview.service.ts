import { Injectable } from '@nestjs/common'

interface InterviewQuestion {
  id: string
  question: string
  type: 'text' | 'choice' | 'multiple_choice' | 'number'
  options?: string[]
  required: boolean
  followUp?: string[]
}

@Injectable()
export class InterviewService {
  private interviewSessions = new Map<string, any>()

  private questions: InterviewQuestion[] = [
    {
      id: 'business_type',
      question: 'What type of business are you running?',
      type: 'choice',
      options: [
        'E-commerce/Retail',
        'Service-based Business',
        'SaaS/Technology',
        'Healthcare',
        'Education',
        'Manufacturing',
        'Consulting',
        'Other'
      ],
      required: true,
    },
    {
      id: 'business_size',
      question: 'How many employees do you have?',
      type: 'choice',
      options: ['1-5', '6-20', '21-50', '51-100', '100+'],
      required: true,
    },
    {
      id: 'current_challenges',
      question: 'What are your biggest operational challenges?',
      type: 'multiple_choice',
      options: [
        'Customer management',
        'Order processing',
        'Employee management',
        'Financial tracking',
        'Inventory management',
        'Customer support',
        'Marketing automation',
        'Reporting and analytics'
      ],
      required: true,
    },
    {
      id: 'existing_tools',
      question: 'What tools are you currently using?',
      type: 'text',
      required: false,
    },
    {
      id: 'goals',
      question: 'What are your main goals for the next 6 months?',
      type: 'text',
      required: true,
    },
    {
      id: 'automation_priority',
      question: 'Which processes would you most like to automate?',
      type: 'multiple_choice',
      options: [
        'Customer onboarding',
        'Order fulfillment',
        'Invoice generation',
        'Customer support tickets',
        'Appointment scheduling',
        'Inventory alerts',
        'Employee onboarding',
        'Reporting'
      ],
      required: true,
    },
  ]

  async startInterview(tenantId: string): Promise<any> {
    const sessionId = `interview_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const session = {
      sessionId,
      tenantId,
      currentQuestionIndex: 0,
      responses: {},
      startedAt: new Date(),
      completed: false,
    }

    this.interviewSessions.set(sessionId, session)

    return {
      sessionId,
      question: this.questions[0],
      progress: {
        current: 1,
        total: this.questions.length,
        percentage: Math.round((1 / this.questions.length) * 100),
      },
    }
  }

  async processResponse(tenantId: string, sessionId: string, response: any): Promise<any> {
    const session = this.interviewSessions.get(sessionId)
    
    if (!session || session.tenantId !== tenantId) {
      throw new Error('Invalid session')
    }

    // Store the response
    const currentQuestion = this.questions[session.currentQuestionIndex]
    session.responses[currentQuestion.id] = response

    // Move to next question
    session.currentQuestionIndex++

    if (session.currentQuestionIndex >= this.questions.length) {
      // Interview completed
      session.completed = true
      session.completedAt = new Date()

      const analysis = this.analyzeResponses(session.responses)
      
      return {
        completed: true,
        analysis,
        recommendedModules: analysis.modules,
        nextSteps: 'System generation will begin automatically.',
      }
    }

    // Return next question
    const nextQuestion = this.questions[session.currentQuestionIndex]
    
    return {
      sessionId,
      question: nextQuestion,
      progress: {
        current: session.currentQuestionIndex + 1,
        total: this.questions.length,
        percentage: Math.round(((session.currentQuestionIndex + 1) / this.questions.length) * 100),
      },
    }
  }

  private analyzeResponses(responses: Record<string, any>): any {
    const modules = []
    const businessType = responses.business_type
    const challenges = responses.current_challenges || []
    const automationPriority = responses.automation_priority || []

    // Determine required modules based on responses
    if (challenges.includes('Customer management') || automationPriority.includes('Customer onboarding')) {
      modules.push({
        name: 'CRM',
        description: 'Customer Relationship Management system',
        features: ['Contact management', 'Lead tracking', 'Customer history'],
        priority: 'high',
      })
    }

    if (businessType === 'E-commerce/Retail' || challenges.includes('Order processing')) {
      modules.push({
        name: 'Orders',
        description: 'Order processing and inventory management',
        features: ['Order tracking', 'Inventory management', 'Payment processing'],
        priority: 'high',
      })
    }

    if (challenges.includes('Employee management') || automationPriority.includes('Employee onboarding')) {
      modules.push({
        name: 'HR',
        description: 'Human Resources management',
        features: ['Employee records', 'Payroll', 'Leave management'],
        priority: 'medium',
      })
    }

    if (challenges.includes('Customer support') || automationPriority.includes('Customer support tickets')) {
      modules.push({
        name: 'Support',
        description: 'Customer support and ticketing system',
        features: ['Ticket management', 'Knowledge base', 'Live chat'],
        priority: 'high',
      })
    }

    if (challenges.includes('Financial tracking') || automationPriority.includes('Invoice generation')) {
      modules.push({
        name: 'Finance',
        description: 'Financial management and accounting',
        features: ['Invoice generation', 'Expense tracking', 'Financial reports'],
        priority: 'medium',
      })
    }

    // Always include basic modules
    if (!modules.find(m => m.name === 'CRM')) {
      modules.push({
        name: 'CRM',
        description: 'Basic customer management',
        features: ['Contact management'],
        priority: 'medium',
      })
    }

    return {
      businessProfile: {
        type: businessType,
        size: responses.business_size,
        goals: responses.goals,
      },
      challenges: challenges,
      automationNeeds: automationPriority,
      modules,
      recommendations: this.generateRecommendations(responses),
    }
  }

  private generateRecommendations(responses: Record<string, any>): string[] {
    const recommendations = []

    if (responses.business_size === '1-5') {
      recommendations.push('Start with CRM and basic automation to establish good data practices')
    }

    if (responses.current_challenges?.includes('Customer support')) {
      recommendations.push('Implement AI-powered customer support to handle common inquiries')
    }

    if (responses.automation_priority?.includes('Reporting')) {
      recommendations.push('Set up automated dashboards for real-time business insights')
    }

    recommendations.push('Consider implementing workflow automation for your most time-consuming processes')

    return recommendations
  }
}