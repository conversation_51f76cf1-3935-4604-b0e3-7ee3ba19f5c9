'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Wrench, 
  Play, 
  Edit, 
  Trash2, 
  <PERSON><PERSON>,
  Eye,
  Globe,
  Lock
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { ToolTestDialog } from '@/components/tools/ToolTestDialog'
import { ToolMetricsDialog } from '@/components/tools/ToolMetricsDialog'
import { apiClient } from '@/lib/api-client'
import { formatDistanceToNow } from 'date-fns'
import toast from 'react-hot-toast'

interface Tool {
  id: string
  name: string
  description: string
  type: string
  category: string
  isPublic: boolean
  creator: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  _count: {
    executions: number
  }
  createdAt: string
  updatedAt: string
}

const toolCategories = [
  { value: 'all', label: 'All Categories' },
  { value: 'BUSINESS', label: 'Business' },
  { value: 'AI', label: 'AI' },
  { value: 'DEVOPS', label: 'DevOps' },
  { value: 'CRM', label: 'CRM' },
  { value: 'COMMUNICATION', label: 'Communication' },
  { value: 'FINANCE', label: 'Finance' },
  { value: 'PRODUCTIVITY', label: 'Productivity' },
  { value: 'ANALYTICS', label: 'Analytics' },
  { value: 'WEB_SCRAPING', label: 'Web Scraping' },
  { value: 'CUSTOM', label: 'Custom' },
]

const toolTypes = [
  { value: 'all', label: 'All Types' },
  { value: 'REST_API', label: 'REST API' },
  { value: 'GRAPHQL', label: 'GraphQL' },
  { value: 'PYTHON_SCRIPT', label: 'Python Script' },
  { value: 'BROWSER_ACTION', label: 'Browser Action' },
  { value: 'WEBHOOK', label: 'Webhook' },
  { value: 'DATABASE', label: 'Database' },
]

export default function ToolsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')
  const [testingTool, setTestingTool] = useState<Tool | null>(null)
  const [viewingMetrics, setViewingMetrics] = useState<Tool | null>(null)
  const router = useRouter()

  const { data: tools, isLoading, refetch } = useQuery({
    queryKey: ['tools', categoryFilter, typeFilter],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (categoryFilter !== 'all') params.append('category', categoryFilter)
      if (typeFilter !== 'all') params.append('type', typeFilter)
      
      const response = await apiClient.get(`/api/tools?${params.toString()}`)
      return response.data
    }
  })

  const filteredTools = tools?.filter((tool: Tool) => {
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description?.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesSearch
  })

  const handleDeleteTool = async (toolId: string) => {
    if (confirm('Are you sure you want to delete this tool?')) {
      try {
        await apiClient.delete(`/api/tools/${toolId}`)
        toast.success('Tool deleted successfully')
        refetch()
      } catch (error) {
        toast.error('Failed to delete tool')
      }
    }
  }

  const handleCloneTool = async (tool: Tool) => {
    try {
      const cloneData = {
        ...tool,
        name: `${tool.name} (Copy)`,
        id: undefined,
        createdAt: undefined,
        updatedAt: undefined,
      }
      await apiClient.post('/api/tools', cloneData)
      toast.success('Tool cloned successfully')
      refetch()
    } catch (error) {
      toast.error('Failed to clone tool')
    }
  }

  const getToolTypeColor = (type: string) => {
    const colors = {
      REST_API: 'bg-blue-100 text-blue-800',
      GRAPHQL: 'bg-purple-100 text-purple-800',
      PYTHON_SCRIPT: 'bg-green-100 text-green-800',
      BROWSER_ACTION: 'bg-orange-100 text-orange-800',
      WEBHOOK: 'bg-indigo-100 text-indigo-800',
      DATABASE: 'bg-red-100 text-red-800',
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      BUSINESS: 'bg-emerald-100 text-emerald-800',
      AI: 'bg-violet-100 text-violet-800',
      DEVOPS: 'bg-slate-100 text-slate-800',
      CRM: 'bg-cyan-100 text-cyan-800',
      COMMUNICATION: 'bg-pink-100 text-pink-800',
      FINANCE: 'bg-yellow-100 text-yellow-800',
      PRODUCTIVITY: 'bg-lime-100 text-lime-800',
      ANALYTICS: 'bg-teal-100 text-teal-800',
      WEB_SCRAPING: 'bg-amber-100 text-amber-800',
      CUSTOM: 'bg-gray-100 text-gray-800',
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Tools</h1>
          <Button disabled>
            <Plus className="w-4 h-4 mr-2" />
            Add Tool
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-3/4" />
                <div className="h-4 bg-gray-200 rounded w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded" />
                  <div className="h-4 bg-gray-200 rounded w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Tools</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your tools and API integrations
          </p>
        </div>
        <Button onClick={() => router.push('/dashboard/tools/new')}>
          <Plus className="w-4 h-4 mr-2" />
          Add Tool
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search tools..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-48">
            <Filter className="w-4 h-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {toolCategories.map((category) => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {toolTypes.map((type) => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTools?.map((tool: Tool) => (
          <Card key={tool.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                    <Wrench className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate flex items-center gap-2">
                      {tool.name}
                      {tool.isPublic ? (
                        <Globe className="w-4 h-4 text-green-600" />
                      ) : (
                        <Lock className="w-4 h-4 text-gray-400" />
                      )}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={getCategoryColor(tool.category)}>
                        {tool.category}
                      </Badge>
                      <Badge variant="outline" className={getToolTypeColor(tool.type)}>
                        {tool.type.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setTestingTool(tool)}>
                      <Play className="w-4 h-4 mr-2" />
                      Test Tool
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setViewingMetrics(tool)}>
                      <Eye className="w-4 h-4 mr-2" />
                      View Metrics
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => router.push(`/dashboard/tools/${tool.id}`)}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleCloneTool(tool)}>
                      <Copy className="w-4 h-4 mr-2" />
                      Clone
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => handleDeleteTool(tool.id)}
                      className="text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <CardDescription className="line-clamp-2">
                {tool.description || 'No description provided'}
              </CardDescription>

              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Type:</span>
                  <span className="font-medium">{tool.type.replace('_', ' ')}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Executions:</span>
                  <span className="font-medium">{tool._count?.executions || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Visibility:</span>
                  <span className="font-medium">
                    {tool.isPublic ? 'Public' : 'Private'}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between pt-3 border-t">
                <div className="flex items-center gap-2">
                  <Avatar className="w-6 h-6">
                    <AvatarImage src={tool.creator.avatar} />
                    <AvatarFallback className="text-xs">
                      {getUserInitials(tool.creator.name)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {tool.creator.name}
                  </span>
                </div>
                <span className="text-xs text-gray-500">
                  {formatDistanceToNow(new Date(tool.createdAt), { addSuffix: true })}
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTools?.length === 0 && (
        <div className="text-center py-12">
          <Wrench className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No tools found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {searchTerm || categoryFilter !== 'all' || typeFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Get started by adding your first tool'
            }
          </p>
          <Button onClick={() => router.push('/dashboard/tools/new')}>
            <Plus className="w-4 h-4 mr-2" />
            Add Tool
          </Button>
        </div>
      )}

      {/* Test Tool Dialog */}
      {testingTool && (
        <ToolTestDialog
          tool={testingTool}
          open={!!testingTool}
          onOpenChange={(open) => !open && setTestingTool(null)}
        />
      )}

      {/* Tool Metrics Dialog */}
      {viewingMetrics && (
        <ToolMetricsDialog
          tool={viewingMetrics}
          open={!!viewingMetrics}
          onOpenChange={(open) => !open && setViewingMetrics(null)}
        />
      )}
    </div>
  )
}