import { Injectable } from '@nestjs/common'
import { InterviewService } from './interview.service'
import { ModuleGeneratorService } from './module-generator.service'

@Injectable()
export class BusinessService {
  constructor(
    private interviewService: InterviewService,
    private moduleGenerator: ModuleGeneratorService,
  ) {}

  async startInterview(tenantId: string): Promise<any> {
    return this.interviewService.startInterview(tenantId)
  }

  async processInterviewResponse(
    tenantId: string, 
    sessionId: string, 
    response: any
  ): Promise<any> {
    return this.interviewService.processResponse(tenantId, sessionId, response)
  }

  async generateBusinessSystem(tenantId: string, interviewData: any): Promise<any> {
    return this.moduleGenerator.generateSystem(tenantId, interviewData)
  }

  async getSystemStatus(tenantId: string): Promise<any> {
    return {
      status: 'active',
      modules: ['CRM', 'Orders', 'Support'],
      generatedAt: new Date(),
      lastUpdated: new Date(),
    }
  }
}