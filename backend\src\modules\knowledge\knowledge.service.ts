import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { KnowledgeDocument } from './entities/knowledge-document.entity'

@Injectable()
export class KnowledgeService {
  constructor(
    @InjectRepository(KnowledgeDocument)
    private documentsRepository: Repository<KnowledgeDocument>,
  ) {}

  async uploadDocument(file: any, tenantId: string, userId: string): Promise<KnowledgeDocument> {
    // Process document and extract text
    const content = await this.extractContent(file)
    const chunks = this.chunkContent(content)
    
    const document = this.documentsRepository.create({
      filename: file.filename,
      originalName: file.originalname,
      mimeType: file.mimetype,
      size: file.size,
      tenantId,
      uploadedBy: userId,
      content,
      chunks,
      status: 'processing',
    })

    const savedDoc = await this.documentsRepository.save(document)
    
    // Process embeddings asynchronously
    this.processEmbeddings(savedDoc.id)
    
    return savedDoc
  }

  async findAll(tenantId: string): Promise<KnowledgeDocument[]> {
    return this.documentsRepository.find({
      where: { tenantId },
      order: { createdAt: 'DESC' }
    })
  }

  async findOne(id: string): Promise<KnowledgeDocument> {
    return this.documentsRepository.findOne({ where: { id } })
  }

  async remove(id: string): Promise<void> {
    await this.documentsRepository.delete(id)
  }

  async search(query: string, tenantId: string): Promise<any[]> {
    // For demo - return mock search results
    return [
      {
        documentId: '1',
        content: 'Sample content matching the query',
        relevance: 0.89,
        source: 'document1.pdf',
      }
    ]
  }

  private async extractContent(file: any): Promise<string> {
    // Mock content extraction - in production, use appropriate parsers
    return `Extracted content from ${file.originalname}`
  }

  private chunkContent(content: string): string[] {
    // Simple chunking - split by sentences or paragraphs
    return content.split('\n').filter(chunk => chunk.trim().length > 0)
  }

  private async processEmbeddings(documentId: string): Promise<void> {
    // Mock embedding processing
    setTimeout(async () => {
      await this.documentsRepository.update(documentId, { 
        status: 'completed',
        processedAt: new Date()
      })
    }, 2000)
  }
}