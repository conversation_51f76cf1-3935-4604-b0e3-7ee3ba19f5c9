'use client'

import { motion } from 'framer-motion'
import { 
  Brain, Database, Workflow, MessageSquare, 
  Shield, Zap, Code, BarChart3 
} from 'lucide-react'

const features = [
  {
    icon: Brain,
    title: 'AI Business Interview',
    description: 'Intelligent questioning system that understands your business needs and automatically generates required modules.',
    color: 'text-blue-500'
  },
  {
    icon: Database,
    title: 'Dynamic Schema Builder',
    description: 'Auto-create database models, API endpoints, and UI forms based on your business requirements.',
    color: 'text-green-500'
  },
  {
    icon: Workflow,
    title: 'Visual Workflow Builder',
    description: 'Drag-and-drop workflow designer with conditional logic, agent sequences, and real-time execution.',
    color: 'text-purple-500'
  },
  {
    icon: MessageSquare,
    title: 'Embeddable Chat Widget',
    description: 'Deploy AI assistants on any website with customizable widgets powered by your business knowledge.',
    color: 'text-orange-500'
  },
  {
    icon: Shield,
    title: 'Enterprise Security',
    description: 'Multi-tenant architecture with role-based access, audit logs, and compliance-ready infrastructure.',
    color: 'text-red-500'
  },
  {
    icon: Zap,
    title: 'Real-time Execution',
    description: 'WebSocket-powered agent orchestration with session persistence and cross-module event system.',
    color: 'text-yellow-500'
  },
  {
    icon: Code,
    title: 'Internal API Generation',
    description: 'Automatically generate REST APIs for all modules with OpenAPI documentation and authentication.',
    color: 'text-indigo-500'
  },
  {
    icon: BarChart3,
    title: 'Analytics & Insights',
    description: 'Built-in reporting, performance metrics, and business intelligence across all generated modules.',
    color: 'text-pink-500'
  }
]

export function FeaturesSection() {
  return (
    <section className="py-20 lg:py-32 relative">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-5xl font-bold text-white mb-6"
          >
            Everything You Need to Build
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              {' '}Smart Business Systems
            </span>
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            From AI-powered interviews to production-ready deployments, AxientOS provides 
            the complete toolkit for modern business automation.
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="glass-effect rounded-xl p-6 hover:scale-105 transition-transform duration-300"
            >
              <feature.icon className={`w-12 h-12 ${feature.color} mb-4`} />
              <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
              <p className="text-gray-300 text-sm leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}