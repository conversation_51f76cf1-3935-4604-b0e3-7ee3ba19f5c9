import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAgentDto, UpdateAgentDto } from './dto/agent.dto';
import { AIProviderService } from '../providers/ai/ai-provider.service';

@Injectable()
export class AgentsService {
  constructor(
    private prisma: PrismaService,
    private aiProvider: AIProviderService,
  ) {}

  async create(createAgentDto: CreateAgentDto, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user?.workspaceId) {
      throw new ForbiddenException('User not associated with a workspace');
    }

    const agent = await this.prisma.agent.create({
      data: {
        ...createAgentDto,
        creatorId: userId,
        workspaceId: user.workspaceId,
        config: createAgentDto.config || {},
        personality: createAgentDto.personality || {},
      },
    });

    // Add tools if provided
    if (createAgentDto.toolIds && createAgentDto.toolIds.length > 0) {
      await this.addTools(agent.id, createAgentDto.toolIds);
    }

    return this.findOne(agent.id);
  }

  async findAll(workspaceId: string, isPublic?: boolean) {
    const where: any = {
      OR: [
        { workspaceId },
        { isPublic: true },
      ],
    };

    if (isPublic !== undefined) {
      where.isPublic = isPublic;
    }

    return this.prisma.agent.findMany({
      where,
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        tools: {
          include: {
            tool: true,
          },
        },
        _count: {
          select: { executions: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: string) {
    const agent = await this.prisma.agent.findUnique({
      where: { id },
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        tools: {
          include: {
            tool: true,
          },
        },
        executions: {
          take: 10,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            status: true,
            tokensUsed: true,
            createdAt: true,
            completedAt: true,
          },
        },
        _count: {
          select: { executions: true },
        },
      },
    });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    return agent;
  }

  async update(id: string, updateAgentDto: UpdateAgentDto, userId: string) {
    const agent = await this.prisma.agent.findUnique({
      where: { id },
    });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    // Check if user owns the agent or is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (agent.creatorId !== userId && user.role !== 'ADMIN') {
      throw new ForbiddenException('Not authorized to update this agent');
    }

    const updatedAgent = await this.prisma.agent.update({
      where: { id },
      data: {
        ...updateAgentDto,
        config: updateAgentDto.config || agent.config,
        personality: updateAgentDto.personality || agent.personality,
      },
    });

    // Update tools if provided
    if (updateAgentDto.toolIds !== undefined) {
      await this.updateTools(id, updateAgentDto.toolIds);
    }

    return this.findOne(id);
  }

  async remove(id: string, userId: string) {
    const agent = await this.prisma.agent.findUnique({
      where: { id },
    });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (agent.creatorId !== userId && user.role !== 'ADMIN') {
      throw new ForbiddenException('Not authorized to delete this agent');
    }

    await this.prisma.agent.delete({
      where: { id },
    });

    return { message: 'Agent deleted successfully' };
  }

  async testAgent(id: string, prompt: string, userId: string) {
    const agent = await this.findOne(id);
    
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (agent.workspaceId !== user.workspaceId && !agent.isPublic) {
      throw new ForbiddenException('Not authorized to test this agent');
    }

    // Prepare messages
    const messages = [
      {
        role: 'system' as const,
        content: agent.personality?.systemPrompt || 'You are a helpful AI assistant.',
      },
      {
        role: 'user' as const,
        content: prompt,
      },
    ];

    // Execute AI request
    const response = await this.aiProvider.generateResponse(
      'openai', // Default provider, should be configurable
      agent.model,
      messages,
      {
        temperature: agent.temperature,
        maxTokens: agent.maxTokens,
      }
    );

    // Record execution
    await this.prisma.agentExecution.create({
      data: {
        agentId: id,
        prompt,
        response: response.content,
        metadata: { usage: response.usage, testMode: true },
        status: 'COMPLETED',
        tokensUsed: response.usage?.totalTokens,
        completedAt: new Date(),
      },
    });

    return {
      response: response.content,
      usage: response.usage,
      metadata: {
        model: response.model,
        finishReason: response.finishReason,
      },
    };
  }

  async getAgentMetrics(id: string, timeRange: string = '7d') {
    const since = new Date();
    if (timeRange === '24h') {
      since.setHours(since.getHours() - 24);
    } else if (timeRange === '7d') {
      since.setDate(since.getDate() - 7);
    } else if (timeRange === '30d') {
      since.setDate(since.getDate() - 30);
    }

    const executions = await this.prisma.agentExecution.findMany({
      where: {
        agentId: id,
        createdAt: { gte: since },
      },
    });

    const total = executions.length;
    const successful = executions.filter(e => e.status === 'COMPLETED').length;
    const totalTokens = executions.reduce((sum, e) => sum + (e.tokensUsed || 0), 0);
    const avgResponseTime = executions.length > 0 
      ? executions.reduce((sum, e) => {
          const duration = e.completedAt ? 
            new Date(e.completedAt).getTime() - new Date(e.createdAt).getTime() : 0;
          return sum + duration;
        }, 0) / executions.length
      : 0;

    return {
      totalExecutions: total,
      successfulExecutions: successful,
      failedExecutions: total - successful,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      totalTokensUsed: totalTokens,
      averageResponseTime: Math.round(avgResponseTime),
      timeRange,
    };
  }

  private async addTools(agentId: string, toolIds: string[]) {
    const agentTools = toolIds.map(toolId => ({
      agentId,
      toolId,
      config: {},
    }));

    await this.prisma.agentTool.createMany({
      data: agentTools,
      skipDuplicates: true,
    });
  }

  private async updateTools(agentId: string, toolIds: string[]) {
    // Remove existing tools
    await this.prisma.agentTool.deleteMany({
      where: { agentId },
    });

    // Add new tools
    if (toolIds.length > 0) {
      await this.addTools(agentId, toolIds);
    }
  }
}