import { Module } from '@nestjs/common';
import { WorkflowsService } from './workflows.service';
import { WorkflowsController } from './workflows.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { WorkflowEngineService } from '../execution/workflow-engine.service';
import { ToolsModule } from '../tools/tools.module';
import { WebSocketModule } from '../websocket/websocket.module';
import { AIProviderModule } from '../../providers/ai/ai-provider.module';

@Module({
  imports: [PrismaModule, ToolsModule, WebSocketModule, AIProviderModule],
  controllers: [WorkflowsController],
  providers: [WorkflowsService, WorkflowEngineService],
  exports: [WorkflowsService, WorkflowEngineService],
})
export class WorkflowsModule {}