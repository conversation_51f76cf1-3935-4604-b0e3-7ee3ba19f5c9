import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as FormData from 'form-data';

export interface ToolExecutionResult {
  success: boolean;
  output: any;
  error?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class ToolExecutorService {
  private readonly logger = new Logger(ToolExecutorService.name);

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {}

  async executeTool(
    toolId: string,
    input: any,
    executionId?: string,
  ): Promise<ToolExecutionResult> {
    const tool = await this.prisma.tool.findUnique({
      where: { id: toolId },
    });

    if (!tool) {
      throw new Error(`Tool ${toolId} not found`);
    }

    this.logger.log(`Executing tool ${tool.name} (${tool.type})`);

    const startTime = Date.now();
    let result: ToolExecutionResult;

    try {
      // Validate input against schema
      if (tool.schema && tool.schema.input) {
        this.validateInput(input, tool.schema.input);
      }

      switch (tool.type) {
        case 'REST_API':
          result = await this.executeRestAPI(tool, input);
          break;
        case 'GRAPHQL':
          result = await this.executeGraphQL(tool, input);
          break;
        case 'PYTHON_SCRIPT':
          result = await this.executePythonScript(tool, input);
          break;
        case 'BROWSER_ACTION':
          result = await this.executeBrowserAction(tool, input);
          break;
        case 'WEBHOOK':
          result = await this.executeWebhook(tool, input);
          break;
        case 'DATABASE':
          result = await this.executeDatabaseQuery(tool, input);
          break;
        default:
          throw new Error(`Unsupported tool type: ${tool.type}`);
      }

      // Record successful execution
      if (executionId) {
        await this.prisma.toolExecution.create({
          data: {
            toolId,
            executionId,
            status: 'COMPLETED',
            input,
            output: result.output,
            completedAt: new Date(),
          },
        });
      }

      result.metadata = {
        ...result.metadata,
        executionTime: Date.now() - startTime,
      };

      return result;

    } catch (error) {
      this.logger.error(`Tool execution failed:`, error);

      // Record failed execution
      if (executionId) {
        await this.prisma.toolExecution.create({
          data: {
            toolId,
            executionId,
            status: 'FAILED',
            input,
            error: error.message,
            completedAt: new Date(),
          },
        });
      }

      return {
        success: false,
        output: null,
        error: error.message,
        metadata: {
          executionTime: Date.now() - startTime,
        },
      };
    }
  }

  private async executeRestAPI(tool: any, input: any): Promise<ToolExecutionResult> {
    const config = tool.config;
    const { url, method = 'GET', headers = {}, auth } = config;

    // Build request configuration
    const requestConfig: any = {
      method: method.toUpperCase(),
      url: this.interpolateString(url, input),
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    };

    // Add authentication
    if (auth) {
      if (auth.type === 'bearer') {
        requestConfig.headers.Authorization = `Bearer ${auth.token}`;
      } else if (auth.type === 'api_key') {
        if (auth.location === 'header') {
          requestConfig.headers[auth.key] = auth.value;
        } else if (auth.location === 'query') {
          requestConfig.params = { [auth.key]: auth.value };
        }
      } else if (auth.type === 'basic') {
        requestConfig.auth = {
          username: auth.username,
          password: auth.password,
        };
      }
    }

    // Add body for POST/PUT requests
    if (['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      requestConfig.data = input.body || input;
    }

    // Add query parameters
    if (input.params) {
      requestConfig.params = { ...requestConfig.params, ...input.params };
    }

    const response = await axios(requestConfig);

    return {
      success: true,
      output: {
        status: response.status,
        headers: response.headers,
        data: response.data,
      },
      metadata: {
        method: requestConfig.method,
        url: requestConfig.url,
        statusCode: response.status,
      },
    };
  }

  private async executeGraphQL(tool: any, input: any): Promise<ToolExecutionResult> {
    const config = tool.config;
    const { endpoint, headers = {}, auth } = config;

    const requestHeaders = {
      'Content-Type': 'application/json',
      ...headers,
    };

    // Add authentication
    if (auth?.type === 'bearer') {
      requestHeaders.Authorization = `Bearer ${auth.token}`;
    }

    const response = await axios.post(endpoint, {
      query: input.query,
      variables: input.variables || {},
    }, {
      headers: requestHeaders,
    });

    if (response.data.errors) {
      throw new Error(`GraphQL Error: ${JSON.stringify(response.data.errors)}`);
    }

    return {
      success: true,
      output: response.data.data,
      metadata: {
        endpoint,
        hasErrors: !!response.data.errors,
      },
    };
  }

  private async executePythonScript(tool: any, input: any): Promise<ToolExecutionResult> {
    // Note: In production, this would execute in a sandboxed environment
    // For now, we'll simulate Python script execution
    const config = tool.config;
    const { script, timeout = 30000 } = config;

    // This is a simplified implementation
    // In production, use a proper sandboxed Python execution environment
    throw new Error('Python script execution not implemented in this demo environment');
  }

  private async executeBrowserAction(tool: any, input: any): Promise<ToolExecutionResult> {
    // Note: In production, this would use Puppeteer or Playwright
    // For now, we'll simulate browser actions
    const config = tool.config;
    const { action, selector, value } = input;

    // This is a simplified implementation
    // In production, use proper browser automation
    return {
      success: true,
      output: {
        action: `Simulated ${action} on ${selector}`,
        value,
      },
      metadata: {
        browser: 'simulated',
        action,
        selector,
      },
    };
  }

  private async executeWebhook(tool: any, input: any): Promise<ToolExecutionResult> {
    const config = tool.config;
    const { url, method = 'POST', headers = {} } = config;

    const response = await axios({
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      data: input,
    });

    return {
      success: true,
      output: response.data,
      metadata: {
        statusCode: response.status,
        method,
        url,
      },
    };
  }

  private async executeDatabaseQuery(tool: any, input: any): Promise<ToolExecutionResult> {
    // Note: In production, this would execute actual database queries
    // with proper security and sandboxing
    const config = tool.config;
    const { query, parameters } = input;

    // This is a simplified implementation
    // In production, use proper database connection with security measures
    throw new Error('Database query execution not implemented in this demo environment');
  }

  private validateInput(input: any, schema: any): void {
    // Basic schema validation
    // In production, use a proper schema validation library like Zod or Ajv
    if (schema.required) {
      for (const field of schema.required) {
        if (input[field] === undefined) {
          throw new Error(`Required field '${field}' is missing`);
        }
      }
    }
  }

  private interpolateString(template: string, variables: any): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] !== undefined ? variables[key] : match;
    });
  }

  async getToolExecutions(toolId: string, limit: number = 50) {
    return await this.prisma.toolExecution.findMany({
      where: { toolId },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  }

  async getExecutionMetrics(toolId: string, timeRange: string = '24h') {
    const since = new Date();
    if (timeRange === '24h') {
      since.setHours(since.getHours() - 24);
    } else if (timeRange === '7d') {
      since.setDate(since.getDate() - 7);
    } else if (timeRange === '30d') {
      since.setDate(since.getDate() - 30);
    }

    const executions = await this.prisma.toolExecution.findMany({
      where: {
        toolId,
        createdAt: { gte: since },
      },
    });

    const total = executions.length;
    const successful = executions.filter(e => e.status === 'COMPLETED').length;
    const failed = total - successful;

    return {
      total,
      successful,
      failed,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      timeRange,
    };
  }
}