import { Injectable } from '@nestjs/common'

export interface FollowUpAction {
  type: 'question' | 'clarification' | 'confirmation' | 'next_step'
  message: string
  options?: string[]
  required: boolean
}

export interface TaskFlow {
  currentStep: string
  nextSteps: string[]
  completedSteps: string[]
  requiredActions: FollowUpAction[]
}

@Injectable()
export class FollowAgent {
  async manageFlow(context: any, processResult: any): Promise<TaskFlow> {
    const { intent, workflowResult } = processResult
    
    const taskFlow: TaskFlow = {
      currentStep: this.determineCurrentStep(intent, workflowResult),
      nextSteps: [],
      completedSteps: [],
      requiredActions: [],
    }

    // Determine if follow-up actions are needed
    const followUpActions = await this.determineFollowUpActions(context, intent, workflowResult)
    taskFlow.requiredActions = followUpActions

    // Set next steps based on intent and workflow state
    taskFlow.nextSteps = this.determineNextSteps(intent, workflowResult)
    
    // Track completed steps from workflow
    if (workflowResult?.executed && workflowResult.steps) {
      taskFlow.completedSteps = workflowResult.steps
        .filter(step => step.status === 'completed')
        .map(step => step.stepId)
    }

    return taskFlow
  }

  private determineCurrentStep(intent: any, workflowResult: any): string {
    if (!workflowResult?.executed) {
      return 'initial_processing'
    }

    const lastStep = workflowResult.steps?.[workflowResult.steps.length - 1]
    return lastStep?.stepId || 'processing_complete'
  }

  private async determineFollowUpActions(
    context: any, 
    intent: any, 
    workflowResult: any
  ): Promise<FollowUpAction[]> {
    const actions: FollowUpAction[] = []

    // Check if more information is needed
    if (intent.confidence < 0.7) {
      actions.push({
        type: 'clarification',
        message: 'Could you please provide more details about what you\'re looking for?',
        required: true,
      })
    }

    // Check for incomplete workflow steps
    if (workflowResult?.executed) {
      const failedSteps = workflowResult.steps?.filter(step => step.status === 'failed')
      
      if (failedSteps && failedSteps.length > 0) {
        actions.push({
          type: 'question',
          message: 'Some steps couldn\'t be completed. Would you like me to retry or help you with an alternative approach?',
          options: ['Retry failed steps', 'Try alternative approach', 'Get help'],
          required: false,
        })
      }
    }

    // Intent-specific follow-ups
    switch (intent.type) {
      case 'business_query':
        if (!intent.entities.dates) {
          actions.push({
            type: 'question',
            message: 'What time period would you like me to focus on?',
            options: ['Last 7 days', 'Last 30 days', 'This month', 'Custom range'],
            required: false,
          })
        }
        break

      case 'request':
        actions.push({
          type: 'confirmation',
          message: 'Is there anything else I can help you with regarding this request?',
          required: false,
        })
        break

      case 'workflow':
        if (workflowResult?.executed) {
          actions.push({
            type: 'next_step',
            message: 'The workflow has been completed. Would you like to monitor its progress or start another workflow?',
            options: ['Monitor progress', 'Start new workflow', 'View results'],
            required: false,
          })
        }
        break
    }

    return actions
  }

  private determineNextSteps(intent: any, workflowResult: any): string[] {
    const nextSteps: string[] = []

    // Default next steps based on intent
    switch (intent.type) {
      case 'greeting':
        nextSteps.push('Ask how I can help', 'Show available features')
        break

      case 'question':
        nextSteps.push('Provide detailed answer', 'Offer related information')
        break

      case 'business_query':
        nextSteps.push('Generate report', 'Show data visualization', 'Export results')
        break

      case 'request':
        if (workflowResult?.executed) {
          nextSteps.push('Monitor execution', 'Confirm completion', 'Handle any issues')
        } else {
          nextSteps.push('Execute request', 'Gather required information')
        }
        break

      case 'workflow':
        nextSteps.push('Monitor workflow progress', 'Handle workflow results', 'Start dependent workflows')
        break

      case 'help':
        nextSteps.push('Provide guidance', 'Show documentation', 'Offer specific assistance')
        break

      default:
        nextSteps.push('Understand request better', 'Provide general assistance')
    }

    return nextSteps
  }

  async trackTaskProgress(sessionId: string, taskId: string, progress: number): Promise<void> {
    // Implementation for tracking task progress
    console.log(`Task ${taskId} in session ${sessionId}: ${progress}% complete`)
  }

  async getTaskStatus(sessionId: string, taskId: string): Promise<any> {
    // Mock task status
    return {
      taskId,
      status: 'in_progress',
      progress: 75,
      estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes from now
    }
  }
}