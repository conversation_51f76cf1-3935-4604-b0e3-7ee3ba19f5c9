NODE_ENV=development
PORT=3001

# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/axientos?schema=public"

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Secrets
JWT_SECRET=axientos-secret-key-change-in-production
JWT_REFRESH_SECRET=axientos-refresh-secret-change-in-production
JWT_RESET_SECRET=axientos-reset-secret-change-in-production

# Frontend URL
FRONTEND_URL=http://localhost:3000

# AI Providers
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key
MISTRAL_API_KEY=your-mistral-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key
COHERE_API_KEY=your-cohere-api-key
GROQ_API_KEY=your-groq-api-key

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Email (Optional - for production)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=

# AWS (Optional - for production)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
S3_BUCKET=