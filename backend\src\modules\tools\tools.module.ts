import { Module } from '@nestjs/common';
import { ToolsService } from './tools.service';
import { ToolsController } from './tools.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { ToolExecutorService } from '../execution/tool-executor.service';

@Module({
  imports: [PrismaModule],
  controllers: [ToolsController],
  providers: [ToolsService, ToolExecutorService],
  exports: [ToolsService, ToolExecutorService],
})
export class ToolsModule {}