'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { X, Bot, Wrench, GitBranch, Clock, Webhook, Settings, Code, Zap } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Node } from 'reactflow'

const baseNodeSchema = z.object({
  label: z.string().min(1, 'Label is required'),
  description: z.string().optional(),
})

const agentNodeSchema = baseNodeSchema.extend({
  agentId: z.string().min(1, 'Agent is required'),
  prompt: z.string().min(1, 'Prompt is required'),
  provider: z.string().default('openai'),
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().min(1).max(4000).default(2000),
})

const toolNodeSchema = baseNodeSchema.extend({
  toolId: z.string().min(1, 'Tool is required'),
  input: z.record(z.any()).default({}),
})

const conditionNodeSchema = baseNodeSchema.extend({
  condition: z.object({
    variable: z.string().min(1, 'Variable is required'),
    operator: z.enum(['equals', 'not_equals', 'greater_than', 'less_than', 'contains', 'exists']),
    value: z.string(),
  }),
})

const delayNodeSchema = baseNodeSchema.extend({
  delay: z.number().min(0, 'Delay must be positive'),
  unit: z.enum(['ms', 's', 'm', 'h']).default('s'),
})

const webhookNodeSchema = baseNodeSchema.extend({
  url: z.string().url('Invalid URL'),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']).default('POST'),
  headers: z.record(z.string()).default({}),
  body: z.record(z.any()).default({}),
})

interface NodePropertiesPanelProps {
  node: Node
  onUpdate: (nodeId: string, data: any) => void
  onClose: () => void
}

export function NodePropertiesPanel({ node, onUpdate, onClose }: NodePropertiesPanelProps) {
  const [activeTab, setActiveTab] = useState('general')

  const getSchema = () => {
    switch (node.type) {
      case 'agent':
        return agentNodeSchema
      case 'tool':
        return toolNodeSchema
      case 'condition':
        return conditionNodeSchema
      case 'delay':
        return delayNodeSchema
      case 'webhook':
        return webhookNodeSchema
      default:
        return baseNodeSchema
    }
  }

  const form = useForm({
    resolver: zodResolver(getSchema()),
    defaultValues: node.data,
  })

  const { register, handleSubmit, watch, setValue, formState: { errors } } = form

  useEffect(() => {
    // Reset form when node changes
    form.reset(node.data)
  }, [node.id, node.data, form])

  const onSubmit = (data: any) => {
    onUpdate(node.id, data)
  }

  const getNodeIcon = () => {
    switch (node.type) {
      case 'agent':
        return <Bot className="w-5 h-5" />
      case 'tool':
        return <Wrench className="w-5 h-5" />
      case 'condition':
        return <GitBranch className="w-5 h-5" />
      case 'delay':
        return <Clock className="w-5 h-5" />
      case 'webhook':
        return <Webhook className="w-5 h-5" />
      default:
        return <Settings className="w-5 h-5" />
    }
  }

  const getNodeTypeColor = () => {
    switch (node.type) {
      case 'agent':
        return 'text-blue-600 bg-blue-100'
      case 'tool':
        return 'text-orange-600 bg-orange-100'
      case 'condition':
        return 'text-purple-600 bg-purple-100'
      case 'delay':
        return 'text-yellow-600 bg-yellow-100'
      case 'webhook':
        return 'text-indigo-600 bg-indigo-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="w-96 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col h-full">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className={`p-2 rounded-lg ${getNodeTypeColor()}`}>
              {getNodeIcon()}
            </div>
            <div>
              <h3 className="font-semibold text-lg">{node.data.label || 'Node Properties'}</h3>
              <Badge variant="outline" className="text-xs">
                {node.type?.toUpperCase() || 'NODE'}
              </Badge>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <form onSubmit={handleSubmit(onSubmit)} className="p-4 space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="parameters">Parameters</TabsTrigger>
              <TabsTrigger value="conditions">Conditions</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="label">Label</Label>
                    <Input
                      id="label"
                      {...register('label')}
                      placeholder="Enter node label"
                    />
                    {errors.label && (
                      <p className="text-sm text-red-600 mt-1">{errors.label.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      {...register('description')}
                      placeholder="Optional description"
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="parameters" className="space-y-4">
              {node.type === 'agent' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Agent Configuration</CardTitle>
                    <CardDescription>Configure AI agent behavior</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="agentId">Agent</Label>
                      <Select 
                        value={watch('agentId')} 
                        onValueChange={(value) => setValue('agentId', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select an agent" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="agent1">Customer Support Agent</SelectItem>
                          <SelectItem value="agent2">Data Analyst Agent</SelectItem>
                          <SelectItem value="agent3">Content Writer Agent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="prompt">Prompt</Label>
                      <Textarea
                        id="prompt"
                        {...register('prompt')}
                        placeholder="Enter the prompt for the agent"
                        rows={4}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="temperature">Temperature</Label>
                        <Input
                          id="temperature"
                          type="number"
                          step="0.1"
                          min="0"
                          max="2"
                          {...register('temperature', { valueAsNumber: true })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="maxTokens">Max Tokens</Label>
                        <Input
                          id="maxTokens"
                          type="number"
                          min="1"
                          max="4000"
                          {...register('maxTokens', { valueAsNumber: true })}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {node.type === 'tool' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Tool Configuration</CardTitle>
                    <CardDescription>Configure tool execution parameters</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="toolId">Tool</Label>
                      <Select 
                        value={watch('toolId')} 
                        onValueChange={(value) => setValue('toolId', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a tool" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="tool1">Slack Notification</SelectItem>
                          <SelectItem value="tool2">Email Sender</SelectItem>
                          <SelectItem value="tool3">Database Query</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Input Parameters</Label>
                      <div className="mt-2 p-3 border rounded-lg bg-gray-50 dark:bg-gray-900">
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Parameters will be configured based on the selected tool
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {node.type === 'condition' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Condition Logic</CardTitle>
                    <CardDescription>Define the condition for branching</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="variable">Variable</Label>
                      <Input
                        id="variable"
                        {...register('condition.variable')}
                        placeholder="e.g., response.status"
                      />
                    </div>

                    <div>
                      <Label htmlFor="operator">Operator</Label>
                      <Select 
                        value={watch('condition.operator')} 
                        onValueChange={(value) => setValue('condition.operator', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select operator" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="equals">Equals</SelectItem>
                          <SelectItem value="not_equals">Not Equals</SelectItem>
                          <SelectItem value="greater_than">Greater Than</SelectItem>
                          <SelectItem value="less_than">Less Than</SelectItem>
                          <SelectItem value="contains">Contains</SelectItem>
                          <SelectItem value="exists">Exists</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="value">Value</Label>
                      <Input
                        id="value"
                        {...register('condition.value')}
                        placeholder="Comparison value"
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {node.type === 'delay' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Delay Configuration</CardTitle>
                    <CardDescription>Set the delay duration</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="delay">Duration</Label>
                        <Input
                          id="delay"
                          type="number"
                          min="0"
                          {...register('delay', { valueAsNumber: true })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="unit">Unit</Label>
                        <Select 
                          value={watch('unit')} 
                          onValueChange={(value) => setValue('unit', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ms">Milliseconds</SelectItem>
                            <SelectItem value="s">Seconds</SelectItem>
                            <SelectItem value="m">Minutes</SelectItem>
                            <SelectItem value="h">Hours</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {node.type === 'webhook' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Webhook Configuration</CardTitle>
                    <CardDescription>Configure HTTP request details</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="url">URL</Label>
                      <Input
                        id="url"
                        {...register('url')}
                        placeholder="https://api.example.com/webhook"
                      />
                    </div>

                    <div>
                      <Label htmlFor="method">Method</Label>
                      <Select 
                        value={watch('method')} 
                        onValueChange={(value) => setValue('method', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="GET">GET</SelectItem>
                          <SelectItem value="POST">POST</SelectItem>
                          <SelectItem value="PUT">PUT</SelectItem>
                          <SelectItem value="DELETE">DELETE</SelectItem>
                          <SelectItem value="PATCH">PATCH</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Headers</Label>
                      <div className="mt-2 p-3 border rounded-lg bg-gray-50 dark:bg-gray-900">
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          <Code className="w-4 h-4 inline mr-1" />
                          JSON editor for headers
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="conditions" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Execution Conditions</CardTitle>
                  <CardDescription>Configure when this node should execute</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Skip on Previous Failure</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Skip this node if any previous node fails
                      </p>
                    </div>
                    <Switch />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Retry on Failure</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Automatically retry this node if it fails
                      </p>
                    </div>
                    <Switch />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="maxRetries">Max Retries</Label>
                      <Input
                        id="maxRetries"
                        type="number"
                        min="0"
                        max="10"
                        defaultValue="3"
                      />
                    </div>
                    <div>
                      <Label htmlFor="retryDelay">Retry Delay (s)</Label>
                      <Input
                        id="retryDelay"
                        type="number"
                        min="0"
                        defaultValue="5"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex gap-2 pt-4 border-t">
            <Button type="submit" className="flex-1">
              <Zap className="w-4 h-4 mr-2" />
              Apply Changes
            </Button>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </form>
      </ScrollArea>
    </div>
  )
}