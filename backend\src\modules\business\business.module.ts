import { Module } from '@nestjs/common'
import { BusinessService } from './business.service'
import { BusinessController } from './business.controller'
import { InterviewService } from './interview.service'
import { ModuleGeneratorService } from './module-generator.service'

@Module({
  controllers: [BusinessController],
  providers: [BusinessService, InterviewService, ModuleGeneratorService],
  exports: [BusinessService],
})
export class BusinessModule {}