import { Injectable } from '@nestjs/common'
import { IntentAgent } from './agents/intent.agent'
import { RetrieverAgent } from './agents/retriever.agent'
import { ToolAgent } from './agents/tool.agent'
import { WorkflowAgent } from './agents/workflow.agent'
import { MemoryAgent } from './agents/memory.agent'
import { FollowAgent } from './agents/follow.agent'
import { FormatterAgent } from './agents/formatter.agent'
import { GuardrailAgent } from './agents/guardrail.agent'
import { LLMAgent } from './agents/llm.agent'

interface ProcessContext {
  message: string
  tenantId: string
  sessionId: string
  userId: string
}

@Injectable()
export class MCPOrchestrator {
  constructor(
    private intentAgent: IntentAgent,
    private retrieverAgent: RetrieverAgent,
    private toolAgent: ToolAgent,
    private workflowAgent: WorkflowAgent,
    private memoryAgent: MemoryAgent,
    private followAgent: FollowAgent,
    private formatterAgent: FormatterAgent,
    private guardrailAgent: GuardrailAgent,
    private llmAgent: LLMAgent,
  ) {}

  async processMessage(context: ProcessContext) {
    try {
      // 1. Intent Agent - Classify user intent
      const intent = await this.intentAgent.classifyIntent(context.message, context)

      // 2. Retriever Agent - Search knowledge base
      const knowledgeContext = await this.retrieverAgent.retrieveContext(
        context.message,
        intent,
        context,
      )

      // 3. Tool Agent - Determine required tools
      const tools = await this.toolAgent.selectTools(intent, context)

      // 4. Workflow Agent - Execute workflows if needed
      const workflowResult = await this.workflowAgent.executeWorkflow(
        intent,
        tools,
        context,
      )

      // 5. Memory Agent - Preserve context
      await this.memoryAgent.storeContext(context, {
        intent,
        knowledgeContext,
        tools,
        workflowResult,
      })

      // 6. Follow Agent - Manage task flow
      const followUp = await this.followAgent.manageFlow(context, {
        intent,
        workflowResult,
      })

      // 7. Formatter Agent - Structure prompt
      const formattedPrompt = await this.formatterAgent.formatPrompt({
        intent,
        knowledgeContext,
        workflowResult,
        followUp,
        originalMessage: context.message,
      })

      // 8. Guardrail Agent - Safety check
      const safetyCheck = await this.guardrailAgent.validateRequest(
        formattedPrompt,
        context,
      )

      if (!safetyCheck.safe) {
        return {
          success: false,
          message: safetyCheck.reason,
          agentFlow: 'guardrail_blocked',
        }
      }

      // 9. LLM Agent - Generate response
      const response = await this.llmAgent.generateResponse(
        formattedPrompt,
        context,
      )

      // Final formatting
      const finalResponse = await this.formatterAgent.formatResponse(
        response,
        context,
      )

      return {
        success: true,
        response: finalResponse,
        agentFlow: 'complete',
        metadata: {
          intent: intent.type,
          toolsUsed: tools.map(t => t.name),
          workflowExecuted: workflowResult.executed,
          sessionId: context.sessionId,
        },
      }
    } catch (error) {
      console.error('MCP Orchestration Error:', error)
      return {
        success: false,
        message: 'An error occurred while processing your request.',
        error: error.message,
      }
    }
  }
}