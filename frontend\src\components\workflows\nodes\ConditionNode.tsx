'use client'

import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { GitBranch } from 'lucide-react'

interface ConditionNodeData {
  label: string
  condition: {
    variable: string
    operator: string
    value: string
  }
}

export const ConditionNode = memo(({ data, selected }: NodeProps<ConditionNodeData>) => {
  return (
    <div className={`
      relative bg-white dark:bg-gray-800 border-2 rounded-lg shadow-lg min-w-[200px]
      ${selected ? 'border-purple-500' : 'border-gray-200 dark:border-gray-700'}
      transition-all duration-200 hover:shadow-xl
    `}>
      <Handle type="target" position={Position.Top} className="w-3 h-3" />
      
      <div className="p-4">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
            <GitBranch className="w-4 h-4 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
              {data.label}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Condition
            </p>
          </div>
        </div>
        
        {data.condition && (
          <div className="mt-2 text-xs text-gray-600 dark:text-gray-300">
            <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
              {data.condition.variable} {data.condition.operator} {data.condition.value}
            </code>
          </div>
        )}
      </div>
      
      <Handle type="source" position={Position.Bottom} id="true" className="w-3 h-3" style={{ left: '25%' }} />
      <Handle type="source" position={Position.Bottom} id="false" className="w-3 h-3" style={{ left: '75%' }} />
    </div>
  )
})