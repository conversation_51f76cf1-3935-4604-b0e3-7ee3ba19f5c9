'use client'

import { <PERSON><PERSON>, <PERSON><PERSON>, GitBranch, Play, Square, Clock, Webhook } from 'lucide-react'

const nodeTypes = [
  { type: 'start', label: 'Start', icon: Play, color: 'text-green-600', bgColor: 'bg-green-100' },
  { type: 'end', label: 'End', icon: Square, color: 'text-red-600', bgColor: 'bg-red-100' },
  { type: 'agent', label: 'AI Agent', icon: Bo<PERSON>, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { type: 'tool', label: 'Tool', icon: Wrench, color: 'text-orange-600', bgColor: 'bg-orange-100' },
  { type: 'condition', label: 'Condition', icon: GitBranch, color: 'text-purple-600', bgColor: 'bg-purple-100' },
  { type: 'delay', label: 'Delay', icon: Clock, color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
  { type: 'webhook', label: 'Webhook', icon: Webhook, color: 'text-indigo-600', bgColor: 'bg-indigo-100' },
]

export function WorkflowSidebar() {
  const onDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType)
    event.dataTransfer.effectAllowed = 'move'
  }

  return (
    <div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Workflow Nodes
      </h3>
      
      <div className="space-y-2">
        {nodeTypes.map((node) => {
          const Icon = node.icon
          return (
            <div
              key={node.type}
              className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-move hover:shadow-md transition-shadow bg-gray-50 dark:bg-gray-700"
              draggable
              onDragStart={(event) => onDragStart(event, node.type)}
            >
              <div className={`p-2 rounded-lg ${node.bgColor} dark:bg-gray-600`}>
                <Icon className={`w-4 h-4 ${node.color} dark:text-gray-300`} />
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {node.label}
              </span>
            </div>
          )
        })}
      </div>
      
      <div className="mt-8">
        <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
          Instructions
        </h4>
        <div className="text-xs text-gray-600 dark:text-gray-400 space-y-2">
          <p>• Drag nodes from the sidebar to the canvas</p>
          <p>• Connect nodes by dragging from output to input handles</p>
          <p>• Click on nodes to configure their properties</p>
          <p>• Use Start and End nodes to define workflow boundaries</p>
        </div>
      </div>
    </div>
  )
}