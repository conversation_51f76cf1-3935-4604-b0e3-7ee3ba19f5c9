import {
  WebSocketGateway as WSGate<PERSON>,
  WebSocketServer,
  SubscribeMessage,
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

interface SocketSession {
  socketId: string;
  userId?: string;
  workspaceId?: string;
  connectedAt: Date;
  authenticated: boolean;
}

@Injectable()
@WSGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
})
export class WebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebSocketGateway.name);
  private sessions = new Map<string, SocketSession>();

  constructor(private jwtService: JwtService) {}

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
    
    this.sessions.set(client.id, {
      socketId: client.id,
      connectedAt: new Date(),
      authenticated: false,
    });
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    this.sessions.delete(client.id);
  }

  @SubscribeMessage('authenticate')
  async handleAuthentication(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { token: string }
  ) {
    try {
      const payload = this.jwtService.verify(data.token);
      
      const session = this.sessions.get(client.id);
      if (session) {
        session.userId = payload.sub;
        session.workspaceId = payload.workspaceId;
        session.authenticated = true;
        this.sessions.set(client.id, session);
      }

      // Join workspace room
      client.join(`workspace:${payload.workspaceId}`);
      client.join(`user:${payload.sub}`);

      client.emit('authentication_success', { 
        sessionId: client.id,
        status: 'authenticated',
        userId: payload.sub,
        workspaceId: payload.workspaceId,
      });

      this.logger.log(`Client ${client.id} authenticated as user ${payload.sub}`);

    } catch (error) {
      this.logger.error(`Authentication failed for client ${client.id}:`, error);
      client.emit('authentication_error', { error: 'Invalid token' });
    }
  }

  @SubscribeMessage('join_room')
  handleJoinRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { room: string }
  ) {
    const session = this.sessions.get(client.id);
    if (!session?.authenticated) {
      client.emit('error', { message: 'Authentication required' });
      return;
    }

    client.join(data.room);
    client.emit('room_joined', { room: data.room });
    this.logger.log(`Client ${client.id} joined room: ${data.room}`);
  }

  @SubscribeMessage('leave_room')
  handleLeaveRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { room: string }
  ) {
    client.leave(data.room);
    client.emit('room_left', { room: data.room });
    this.logger.log(`Client ${client.id} left room: ${data.room}`);
  }

  // Workflow execution notifications
  notifyWorkflowStarted(executionId: string, workflowId: string) {
    this.server.emit('workflow_started', {
      executionId,
      workflowId,
      timestamp: new Date(),
    });
  }

  notifyWorkflowCompleted(executionId: string, output: any) {
    this.server.emit('workflow_completed', {
      executionId,
      output,
      timestamp: new Date(),
    });
  }

  notifyWorkflowFailed(executionId: string, error: string) {
    this.server.emit('workflow_failed', {
      executionId,
      error,
      timestamp: new Date(),
    });
  }

  notifyNodeCompleted(executionId: string, nodeId: string, result: any) {
    this.server.emit('node_completed', {
      executionId,
      nodeId,
      result,
      timestamp: new Date(),
    });
  }

  // Agent execution notifications
  notifyAgentExecution(agentId: string, execution: any) {
    this.server.emit('agent_execution', {
      agentId,
      execution,
      timestamp: new Date(),
    });
  }

  // Tool execution notifications
  notifyToolExecution(toolId: string, execution: any) {
    this.server.emit('tool_execution', {
      toolId,
      execution,
      timestamp: new Date(),
    });
  }

  // Workspace-specific notifications
  notifyWorkspace(workspaceId: string, event: string, data: any) {
    this.server.to(`workspace:${workspaceId}`).emit(event, {
      ...data,
      timestamp: new Date(),
    });
  }

  // User-specific notifications
  notifyUser(userId: string, event: string, data: any) {
    this.server.to(`user:${userId}`).emit(event, {
      ...data,
      timestamp: new Date(),
    });
  }

  // System-wide notifications
  notifySystem(event: string, data: any) {
    this.server.emit(event, {
      ...data,
      timestamp: new Date(),
    });
  }

  // Get connection statistics
  getConnectionStats() {
    const totalConnections = this.sessions.size;
    const authenticatedConnections = Array.from(this.sessions.values())
      .filter(session => session.authenticated).length;

    const workspaceConnections = new Map<string, number>();
    Array.from(this.sessions.values())
      .filter(session => session.workspaceId)
      .forEach(session => {
        const count = workspaceConnections.get(session.workspaceId!) || 0;
        workspaceConnections.set(session.workspaceId!, count + 1);
      });

    return {
      total: totalConnections,
      authenticated: authenticatedConnections,
      unauthenticated: totalConnections - authenticatedConnections,
      byWorkspace: Object.fromEntries(workspaceConnections),
    };
  }
}