import { Injectable } from '@nestjs/common'

export interface ConversationMemory {
  sessionId: string
  userId: string
  tenantId: string
  messages: Array<{
    role: 'user' | 'assistant'
    content: string
    timestamp: Date
    metadata?: Record<string, any>
  }>
  context: Record<string, any>
  lastAccessed: Date
}

@Injectable()
export class MemoryAgent {
  private sessionMemory = new Map<string, ConversationMemory>()

  async storeContext(context: any, agentData: any): Promise<void> {
    const { sessionId, userId, tenantId, message } = context
    
    let memory = this.sessionMemory.get(sessionId)
    
    if (!memory) {
      memory = {
        sessionId,
        userId,
        tenantId,
        messages: [],
        context: {},
        lastAccessed: new Date(),
      }
    }

    // Store user message
    memory.messages.push({
      role: 'user',
      content: message,
      timestamp: new Date(),
      metadata: {
        intent: agentData.intent,
        toolsAvailable: agentData.tools?.map(t => t.name) || [],
      },
    })

    // Update context with agent processing results
    memory.context = {
      ...memory.context,
      lastIntent: agentData.intent,
      lastKnowledgeContext: agentData.knowledgeContext,
      lastTools: agentData.tools,
      lastWorkflowResult: agentData.workflowResult,
    }

    memory.lastAccessed = new Date()
    this.sessionMemory.set(sessionId, memory)
  }

  async storeResponse(sessionId: string, response: string, metadata?: Record<string, any>): Promise<void> {
    const memory = this.sessionMemory.get(sessionId)
    
    if (memory) {
      memory.messages.push({
        role: 'assistant',
        content: response,
        timestamp: new Date(),
        metadata,
      })
      
      memory.lastAccessed = new Date()
      this.sessionMemory.set(sessionId, memory)
    }
  }

  async getConversationHistory(sessionId: string, limit: number = 10): Promise<ConversationMemory | null> {
    const memory = this.sessionMemory.get(sessionId)
    
    if (!memory) {
      return null
    }

    // Return recent messages
    const recentMessages = memory.messages
      .slice(-limit * 2) // user + assistant pairs
      .slice(-limit)

    return {
      ...memory,
      messages: recentMessages,
      lastAccessed: new Date(),
    }
  }

  async getSessionContext(sessionId: string): Promise<Record<string, any> | null> {
    const memory = this.sessionMemory.get(sessionId)
    return memory?.context || null
  }

  async clearSession(sessionId: string): Promise<void> {
    this.sessionMemory.delete(sessionId)
  }

  async getActiveSessionsCount(): Promise<number> {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    let activeCount = 0

    for (const memory of this.sessionMemory.values()) {
      if (memory.lastAccessed > oneHourAgo) {
        activeCount++
      }
    }

    return activeCount
  }

  async cleanupOldSessions(): Promise<number> {
    const twelveHoursAgo = new Date(Date.now() - 12 * 60 * 60 * 1000)
    let cleanedCount = 0

    for (const [sessionId, memory] of this.sessionMemory.entries()) {
      if (memory.lastAccessed < twelveHoursAgo) {
        this.sessionMemory.delete(sessionId)
        cleanedCount++
      }
    }

    return cleanedCount
  }
}