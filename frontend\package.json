{"name": "@axientos/frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "@next/swc-wasm-nodejs": "14.2.5", "tailwindcss": "^3.4.4", "autoprefixer": "^10.4.19", "postcss": "^8.4.39", "@tailwindcss/forms": "^0.5.7", "@headlessui/react": "^2.1.2", "@heroicons/react": "^2.1.4", "lucide-react": "^0.400.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0", "reactflow": "^11.11.4", "@xyflow/react": "^12.0.2", "socket.io-client": "^4.7.5", "axios": "^1.7.2", "react-hook-form": "^7.52.1", "@hookform/resolvers": "^3.7.0", "zod": "^3.23.8", "@tanstack/react-query": "^5.50.1", "zustand": "^4.5.4", "framer-motion": "^11.3.2", "react-hot-toast": "^2.4.1", "date-fns": "^3.6.0", "@monaco-editor/react": "^4.6.0", "monaco-editor": "^0.50.0", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.13", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "react-dropzone": "^14.2.3", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2"}, "devDependencies": {"@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "typescript": "^5.5.3"}}