import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AIProviderService } from '../providers/ai/ai-provider.service';

export interface RoutingContext {
  message: string;
  userId?: string;
  workspaceId: string;
  sessionContext?: Record<string, any>;
}

export interface RoutingDecision {
  type: 'agent' | 'tool' | 'workflow';
  id: string;
  confidence: number;
  reasoning: string;
  fallback?: RoutingDecision;
}

export interface AgentMatch {
  agent: any;
  score: number;
  reasoning: string;
}

@Injectable()
export class SmartRouterService {
  private readonly logger = new Logger(SmartRouterService.name);

  constructor(
    private prisma: PrismaService,
    private aiProvider: AIProviderService,
  ) {}

  async routeRequest(context: RoutingContext): Promise<RoutingDecision> {
    this.logger.log(`Routing request for workspace ${context.workspaceId}`);

    try {
      // Get available agents and workflows
      const [agents, workflows, tools] = await Promise.all([
        this.getAvailableAgents(context.workspaceId),
        this.getAvailableWorkflows(context.workspaceId),
        this.getAvailableTools(context.workspaceId),
      ]);

      // Analyze intent and requirements
      const intent = await this.analyzeIntent(context.message);
      
      // Score and rank options
      const agentMatches = await this.scoreAgents(agents, intent, context);
      const workflowMatches = await this.scoreWorkflows(workflows, intent, context);
      const toolMatches = await this.scoreTools(tools, intent, context);

      // Find best match
      const bestMatch = this.selectBestMatch(agentMatches, workflowMatches, toolMatches);

      if (!bestMatch) {
        // Fallback to default agent
        const defaultAgent = agents.find(a => a.name.toLowerCase().includes('default')) || agents[0];
        return {
          type: 'agent',
          id: defaultAgent?.id,
          confidence: 0.3,
          reasoning: 'No specific match found, using default agent',
        };
      }

      return bestMatch;

    } catch (error) {
      this.logger.error('Routing failed:', error);
      throw new Error(`Failed to route request: ${error.message}`);
    }
  }

  private async analyzeIntent(message: string): Promise<any> {
    const messages = [
      {
        role: 'system' as const,
        content: `You are an AI intent analyzer. Analyze the user message and extract:
        1. Primary intent (question, request, task, analysis, etc.)
        2. Domain/category (customer service, data analysis, automation, etc.)
        3. Complexity level (simple, medium, complex)
        4. Required capabilities (api_calls, data_processing, calculations, etc.)
        
        Respond in JSON format only.`,
      },
      {
        role: 'user' as const,
        content: `Analyze this message: "${message}"`,
      },
    ];

    try {
      const response = await this.aiProvider.generateResponse(
        'openai',
        'gpt-3.5-turbo',
        messages,
        { temperature: 0.1, maxTokens: 500 }
      );

      return JSON.parse(response.content);
    } catch (error) {
      // Fallback to simple analysis
      return {
        intent: 'general',
        domain: 'general',
        complexity: 'medium',
        capabilities: ['conversation'],
      };
    }
  }

  private async scoreAgents(
    agents: any[],
    intent: any,
    context: RoutingContext,
  ): Promise<AgentMatch[]> {
    const matches: AgentMatch[] = [];

    for (const agent of agents) {
      let score = 0;
      let reasoning = '';

      // Base score for agent type
      if (agent.type === 'CHAT' && intent.intent === 'question') {
        score += 0.3;
        reasoning += 'Chat agent for question; ';
      } else if (agent.type === 'TASK' && intent.intent === 'request') {
        score += 0.4;
        reasoning += 'Task agent for request; ';
      } else if (agent.type === 'ANALYST' && intent.domain === 'data_analysis') {
        score += 0.5;
        reasoning += 'Analyst agent for data analysis; ';
      }

      // Description and name matching
      const description = (agent.description || '').toLowerCase();
      const name = agent.name.toLowerCase();
      const messageLower = context.message.toLowerCase();

      const keywords = messageLower.split(' ').filter(word => word.length > 3);
      let keywordMatches = 0;

      for (const keyword of keywords) {
        if (description.includes(keyword) || name.includes(keyword)) {
          keywordMatches++;
        }
      }

      if (keywordMatches > 0) {
        score += (keywordMatches / keywords.length) * 0.3;
        reasoning += `${keywordMatches} keyword matches; `;
      }

      // Tool availability score
      if (agent.tools && agent.tools.length > 0) {
        const requiredCapabilities = intent.capabilities || [];
        const hasRelevantTools = requiredCapabilities.some(cap =>
          agent.tools.some(tool => tool.tool.category.toLowerCase().includes(cap))
        );
        
        if (hasRelevantTools) {
          score += 0.2;
          reasoning += 'Has relevant tools; ';
        }
      }

      // Recency boost for recently created/updated agents
      const daysSinceUpdate = (Date.now() - new Date(agent.updatedAt).getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceUpdate < 7) {
        score += 0.1;
        reasoning += 'Recently updated; ';
      }

      if (score > 0) {
        matches.push({
          agent,
          score: Math.min(score, 1.0), // Cap at 1.0
          reasoning: reasoning.trim(),
        });
      }
    }

    return matches.sort((a, b) => b.score - a.score);
  }

  private async scoreWorkflows(
    workflows: any[],
    intent: any,
    context: RoutingContext,
  ): Promise<any[]> {
    const matches = [];

    for (const workflow of workflows) {
      let score = 0;
      let reasoning = '';

      // Workflows are better for complex, multi-step tasks
      if (intent.complexity === 'complex') {
        score += 0.4;
        reasoning += 'Complex task suitable for workflow; ';
      }

      // Check if workflow description matches intent
      const description = (workflow.description || '').toLowerCase();
      const messageLower = context.message.toLowerCase();

      if (description.includes('automation') && messageLower.includes('automate')) {
        score += 0.3;
        reasoning += 'Automation workflow for automation request; ';
      }

      // Multi-step indicator
      if (workflow.nodes && workflow.nodes.length > 3) {
        score += 0.2;
        reasoning += 'Multi-step workflow; ';
      }

      if (score > 0) {
        matches.push({
          type: 'workflow',
          id: workflow.id,
          score: Math.min(score, 1.0),
          reasoning: reasoning.trim(),
        });
      }
    }

    return matches.sort((a, b) => b.score - a.score);
  }

  private async scoreTools(
    tools: any[],
    intent: any,
    context: RoutingContext,
  ): Promise<any[]> {
    const matches = [];

    for (const tool of tools) {
      let score = 0;
      let reasoning = '';

      // Direct tool usage for simple, specific tasks
      if (intent.complexity === 'simple' && intent.intent === 'request') {
        score += 0.3;
        reasoning += 'Simple request suitable for direct tool; ';
      }

      // Category matching
      const category = tool.category.toLowerCase();
      const domain = intent.domain || '';

      if (category.includes(domain)) {
        score += 0.4;
        reasoning += `Category matches domain (${domain}); `;
      }

      if (score > 0) {
        matches.push({
          type: 'tool',
          id: tool.id,
          score: Math.min(score, 1.0),
          reasoning: reasoning.trim(),
        });
      }
    }

    return matches.sort((a, b) => b.score - a.score);
  }

  private selectBestMatch(
    agentMatches: any[],
    workflowMatches: any[],
    toolMatches: any[],
  ): RoutingDecision | null {
    // Combine all matches
    const allMatches = [
      ...agentMatches.map(m => ({ ...m, type: 'agent', id: m.agent.id })),
      ...workflowMatches,
      ...toolMatches,
    ].sort((a, b) => b.score - a.score);

    if (allMatches.length === 0) {
      return null;
    }

    const best = allMatches[0];
    const fallback = allMatches.length > 1 ? allMatches[1] : undefined;

    return {
      type: best.type,
      id: best.id,
      confidence: best.score,
      reasoning: best.reasoning,
      fallback: fallback ? {
        type: fallback.type,
        id: fallback.id,
        confidence: fallback.score,
        reasoning: fallback.reasoning,
      } : undefined,
    };
  }

  private async getAvailableAgents(workspaceId: string) {
    return await this.prisma.agent.findMany({
      where: {
        workspaceId,
        isActive: true,
      },
      include: {
        tools: {
          include: {
            tool: true,
          },
        },
      },
    });
  }

  private async getAvailableWorkflows(workspaceId: string) {
    return await this.prisma.workflow.findMany({
      where: {
        workspaceId,
        isActive: true,
      },
      include: {
        nodes: true,
      },
    });
  }

  private async getAvailableTools(workspaceId: string) {
    return await this.prisma.tool.findMany({
      where: {
        OR: [
          { workspaceId, isPublic: false },
          { isPublic: true },
        ],
      },
    });
  }

  async getRoutingAnalytics(workspaceId: string, timeRange: string = '24h') {
    // Implementation for routing analytics
    // This would track routing decisions and their success rates
    return {
      totalRoutes: 0,
      agentRoutes: 0,
      workflowRoutes: 0,
      toolRoutes: 0,
      averageConfidence: 0,
      successRate: 0,
    };
  }
}