import { Injectable } from '@nestjs/common'

export interface FormattedPrompt {
  system: string
  user: string
  context: Record<string, any>
  temperature: number
  maxTokens: number
}

@Injectable()
export class FormatterAgent {
  async formatPrompt(data: {
    intent: any
    knowledgeContext: any
    workflowResult: any
    followUp: any
    originalMessage: string
  }): Promise<FormattedPrompt> {
    const { intent, knowledgeContext, workflowResult, followUp, originalMessage } = data

    // Build system prompt based on context
    const systemPrompt = this.buildSystemPrompt(intent, knowledgeContext, workflowResult)
    
    // Build user prompt with context
    const userPrompt = this.buildUserPrompt(originalMessage, intent, followUp)

    return {
      system: systemPrompt,
      user: userPrompt,
      context: {
        intent: intent.type,
        confidence: intent.confidence,
        hasKnowledge: knowledgeContext.documents.length > 0,
        workflowExecuted: workflowResult.executed,
        followUpRequired: followUp.requiredActions.length > 0,
      },
      temperature: this.determineTemperature(intent),
      maxTokens: this.determineMaxTokens(intent),
    }
  }

  async formatResponse(response: any, context: any): Promise<string> {
    // Format the final response for the user
    if (typeof response === 'string') {
      return this.enhanceResponse(response, context)
    }

    if (response.choices && response.choices[0]) {
      return this.enhanceResponse(response.choices[0].message.content, context)
    }

    return 'I apologize, but I couldn\'t generate a proper response. Please try again.'
  }

  private buildSystemPrompt(intent: any, knowledgeContext: any, workflowResult: any): string {
    let systemPrompt = `You are AxientOS, an AI business assistant powered by advanced MCP (Multi-agent Cognitive Pipeline) architecture. You help businesses with operations, data analysis, and workflow automation.

Key Capabilities:
- Business process automation
- Data analysis and reporting
- Customer support and CRM
- Order and inventory management
- Knowledge base search and retrieval

Current Context:
- User Intent: ${intent.type} (confidence: ${(intent.confidence * 100).toFixed(1)}%)
- Intent Category: ${intent.category}`

    // Add knowledge context if available
    if (knowledgeContext.documents.length > 0) {
      systemPrompt += `\n\nRelevant Knowledge Base Information:
${knowledgeContext.documents
  .map(doc => `- ${doc.content} (Source: ${doc.source})`)
  .join('\n')}`
    }

    // Add workflow results if executed
    if (workflowResult.executed) {
      systemPrompt += `\n\nWorkflow Execution Results:
- Workflow: ${workflowResult.workflowId}
- Total Steps: ${workflowResult.totalSteps}
- Completed Steps: ${workflowResult.steps.filter(s => s.status === 'completed').length}
- Failed Steps: ${workflowResult.steps.filter(s => s.status === 'failed').length}`
    }

    systemPrompt += `\n\nInstructions:
1. Provide helpful, accurate, and contextual responses
2. Use the knowledge base information when relevant
3. Reference workflow results when applicable
4. Be professional but conversational
5. Offer specific next steps when appropriate
6. Ask for clarification if the request is unclear`

    return systemPrompt
  }

  private buildUserPrompt(originalMessage: string, intent: any, followUp: any): string {
    let userPrompt = `User Message: "${originalMessage}"`

    // Add extracted entities if available
    if (intent.entities && Object.keys(intent.entities).length > 0) {
      userPrompt += `\n\nExtracted Information:
${Object.entries(intent.entities)
  .map(([key, value]) => `- ${key}: ${JSON.stringify(value)}`)
  .join('\n')}`
    }

    // Add follow-up context if needed
    if (followUp.requiredActions.length > 0) {
      userPrompt += `\n\nFollow-up Actions Needed:
${followUp.requiredActions
  .map(action => `- ${action.type}: ${action.message}`)
  .join('\n')}`
    }

    return userPrompt
  }

  private enhanceResponse(response: string, context: any): string {
    // Add contextual enhancements
    let enhancedResponse = response

    // Add session context footer for complex operations
    if (context.sessionId && (context.workflowExecuted || context.hasKnowledge)) {
      enhancedResponse += `\n\n---\n*Session ID: ${context.sessionId.slice(-8)} | Powered by AxientOS MCP Pipeline*`
    }

    return enhancedResponse
  }

  private determineTemperature(intent: any): number {
    // Adjust creativity based on intent type
    switch (intent.category) {
      case 'business':
        return 0.3 // More deterministic for business queries
      case 'informational':
        return 0.5 // Balanced for informational responses
      case 'social':
        return 0.7 // More creative for social interactions
      case 'support':
        return 0.4 // Structured but helpful for support
      default:
        return 0.6 // Default balanced approach
    }
  }

  private determineMaxTokens(intent: any): number {
    // Adjust response length based on intent
    switch (intent.type) {
      case 'greeting':
        return 150
      case 'business_query':
        return 800
      case 'workflow':
        return 600
      case 'help':
        return 500
      default:
        return 400
    }
  }
}