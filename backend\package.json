{"name": "@axientos/backend", "version": "1.0.0", "description": "AxientOS Backend - Production MCP Orchestration Engine", "private": true, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@nestjs/common": "^10.3.9", "@nestjs/core": "^10.3.9", "@nestjs/platform-express": "^10.3.9", "@nestjs/platform-socket.io": "^10.3.9", "@nestjs/websockets": "^10.3.9", "@nestjs/config": "^3.2.2", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/bull": "^10.1.1", "@nestjs/cache-manager": "^2.2.2", "@nestjs/swagger": "^7.3.1", "@nestjs/schedule": "^4.0.2", "@prisma/client": "^5.15.0", "prisma": "^5.15.0", "redis": "^4.6.14", "ioredis": "^5.4.1", "bull": "^4.12.4", "bullmq": "^5.8.3", "socket.io": "^4.7.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "class-validator": "^0.14.1", "class-transformer": "^0.5.1", "openai": "^4.52.7", "@anthropic-ai/sdk": "^0.24.3", "@google/generative-ai": "^0.15.0", "@mistralai/mistralai": "^0.4.0", "@huggingface/inference": "^2.7.0", "cohere-ai": "^7.10.6", "groq-sdk": "^0.5.0", "axios": "^1.7.2", "pdf-parse": "^1.1.1", "mammoth": "^1.7.2", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "cheerio": "^1.0.0-rc.12", "uuid": "^10.0.0", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "sharp": "^0.33.4", "zod": "^3.23.8", "zod-to-json-schema": "^3.23.1", "node-cron": "^3.0.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "ws": "^8.17.1", "node-fetch": "^3.3.2", "form-data": "^4.0.0"}, "devDependencies": {"@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.1.1", "@nestjs/testing": "^10.3.9", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.14.10", "@types/supertest": "^6.0.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pdf-parse": "^1.1.4", "@types/multer": "^1.4.11", "@types/mime-types": "^2.1.4", "@types/uuid": "^10.0.0", "@types/node-cron": "^3.0.11", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.3.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}