import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateToolDto, UpdateToolDto } from './dto/tool.dto';
import { ToolExecutorService } from '../execution/tool-executor.service';

@Injectable()
export class ToolsService {
  constructor(
    private prisma: PrismaService,
    private toolExecutor: ToolExecutorService,
  ) {}

  async create(createToolDto: CreateToolDto, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user?.workspaceId) {
      throw new ForbiddenException('User not associated with a workspace');
    }

    const tool = await this.prisma.tool.create({
      data: {
        ...createToolDto,
        creatorId: userId,
        workspaceId: user.workspaceId,
        config: createToolDto.config || {},
        schema: createToolDto.schema || {},
      },
    });

    return this.findOne(tool.id);
  }

  async findAll(workspaceId: string, category?: string, isPublic?: boolean) {
    const where: any = {
      OR: [
        { workspaceId },
        { isPublic: true },
      ],
    };

    if (category) {
      where.category = category;
    }

    if (isPublic !== undefined) {
      where.isPublic = isPublic;
    }

    return this.prisma.tool.findMany({
      where,
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        _count: {
          select: { executions: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: string) {
    const tool = await this.prisma.tool.findUnique({
      where: { id },
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        executions: {
          take: 10,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            status: true,
            createdAt: true,
            completedAt: true,
            error: true,
          },
        },
        _count: {
          select: { executions: true },
        },
      },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    return tool;
  }

  async update(id: string, updateToolDto: UpdateToolDto, userId: string) {
    const tool = await this.prisma.tool.findUnique({
      where: { id },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (tool.creatorId !== userId && user.role !== 'ADMIN') {
      throw new ForbiddenException('Not authorized to update this tool');
    }

    const updatedTool = await this.prisma.tool.update({
      where: { id },
      data: {
        ...updateToolDto,
        config: updateToolDto.config || tool.config,
        schema: updateToolDto.schema || tool.schema,
      },
    });

    return this.findOne(id);
  }

  async remove(id: string, userId: string) {
    const tool = await this.prisma.tool.findUnique({
      where: { id },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (tool.creatorId !== userId && user.role !== 'ADMIN') {
      throw new ForbiddenException('Not authorized to delete this tool');
    }

    await this.prisma.tool.delete({
      where: { id },
    });

    return { message: 'Tool deleted successfully' };
  }

  async testTool(id: string, input: any, userId: string) {
    const tool = await this.findOne(id);
    
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (tool.workspaceId !== user.workspaceId && !tool.isPublic) {
      throw new ForbiddenException('Not authorized to test this tool');
    }

    return this.toolExecutor.executeTool(id, input);
  }

  async getToolMetrics(id: string, timeRange: string = '7d') {
    return this.toolExecutor.getExecutionMetrics(id, timeRange);
  }

  async getCategories() {
    const categories = await this.prisma.tool.groupBy({
      by: ['category'],
      _count: {
        category: true,
      },
    });

    return categories.map(cat => ({
      category: cat.category,
      count: cat._count.category,
    }));
  }

  async validateSchema(schema: any): Promise<boolean> {
    try {
      // Basic schema validation
      if (!schema.input && !schema.output) {
        return false;
      }

      // Validate input schema structure
      if (schema.input) {
        if (!schema.input.type || !schema.input.properties) {
          return false;
        }
      }

      // Validate output schema structure  
      if (schema.output) {
        if (!schema.output.type) {
          return false;
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  }
}