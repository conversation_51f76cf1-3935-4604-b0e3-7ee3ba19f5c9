import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { MistralAI } from '@mistralai/mistralai';
import { HfInference } from '@huggingface/inference';
import { CohereAPI } from 'cohere-ai';
import Groq from 'groq-sdk';

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason?: string;
}

export interface StreamCallback {
  (chunk: string): void;
}

@Injectable()
export class AIProviderService {
  private readonly logger = new Logger(AIProviderService.name);
  private providers: Map<string, any> = new Map();

  constructor(private configService: ConfigService) {
    this.initializeProviders();
  }

  private initializeProviders() {
    // OpenAI
    if (this.configService.get('OPENAI_API_KEY')) {
      this.providers.set('openai', new OpenAI({
        apiKey: this.configService.get('OPENAI_API_KEY'),
      }));
    }

    // Anthropic
    if (this.configService.get('ANTHROPIC_API_KEY')) {
      this.providers.set('anthropic', new Anthropic({
        apiKey: this.configService.get('ANTHROPIC_API_KEY'),
      }));
    }

    // Google Gemini
    if (this.configService.get('GOOGLE_API_KEY')) {
      this.providers.set('google', new GoogleGenerativeAI(
        this.configService.get('GOOGLE_API_KEY')
      ));
    }

    // Mistral
    if (this.configService.get('MISTRAL_API_KEY')) {
      this.providers.set('mistral', new MistralAI(
        this.configService.get('MISTRAL_API_KEY')
      ));
    }

    // HuggingFace
    if (this.configService.get('HUGGINGFACE_API_KEY')) {
      this.providers.set('huggingface', new HfInference(
        this.configService.get('HUGGINGFACE_API_KEY')
      ));
    }

    // Cohere
    if (this.configService.get('COHERE_API_KEY')) {
      this.providers.set('cohere', new CohereAPI({
        token: this.configService.get('COHERE_API_KEY'),
      }));
    }

    // Groq
    if (this.configService.get('GROQ_API_KEY')) {
      this.providers.set('groq', new Groq({
        apiKey: this.configService.get('GROQ_API_KEY'),
      }));
    }

    this.logger.log(`Initialized ${this.providers.size} AI providers`);
  }

  async generateResponse(
    provider: string,
    model: string,
    messages: AIMessage[],
    options: {
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
      streamCallback?: StreamCallback;
    } = {}
  ): Promise<AIResponse> {
    const { temperature = 0.7, maxTokens = 2000, stream = false } = options;

    try {
      switch (provider.toLowerCase()) {
        case 'openai':
          return await this.generateOpenAI(model, messages, { temperature, maxTokens, stream, streamCallback: options.streamCallback });
        
        case 'anthropic':
          return await this.generateAnthropic(model, messages, { temperature, maxTokens, stream, streamCallback: options.streamCallback });
        
        case 'google':
          return await this.generateGoogle(model, messages, { temperature, maxTokens });
        
        case 'mistral':
          return await this.generateMistral(model, messages, { temperature, maxTokens });
        
        case 'groq':
          return await this.generateGroq(model, messages, { temperature, maxTokens });
        
        default:
          throw new Error(`Unsupported AI provider: ${provider}`);
      }
    } catch (error) {
      this.logger.error(`Error generating response with ${provider}:`, error);
      throw error;
    }
  }

  private async generateOpenAI(
    model: string,
    messages: AIMessage[],
    options: any
  ): Promise<AIResponse> {
    const openai = this.providers.get('openai');
    if (!openai) throw new Error('OpenAI provider not configured');

    if (options.stream) {
      return await this.streamOpenAI(openai, model, messages, options);
    }

    const response = await openai.chat.completions.create({
      model,
      messages,
      temperature: options.temperature,
      max_tokens: options.maxTokens,
    });

    return {
      content: response.choices[0].message.content || '',
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0,
      },
      model: response.model,
      finishReason: response.choices[0].finish_reason || 'stop',
    };
  }

  private async streamOpenAI(
    openai: OpenAI,
    model: string,
    messages: AIMessage[],
    options: any
  ): Promise<AIResponse> {
    const stream = await openai.chat.completions.create({
      model,
      messages,
      temperature: options.temperature,
      max_tokens: options.maxTokens,
      stream: true,
    });

    let content = '';
    let usage = { promptTokens: 0, completionTokens: 0, totalTokens: 0 };

    for await (const chunk of stream) {
      const delta = chunk.choices[0]?.delta?.content || '';
      if (delta) {
        content += delta;
        if (options.streamCallback) {
          options.streamCallback(delta);
        }
      }
    }

    return {
      content,
      usage,
      model,
      finishReason: 'stop',
    };
  }

  private async generateAnthropic(
    model: string,
    messages: AIMessage[],
    options: any
  ): Promise<AIResponse> {
    const anthropic = this.providers.get('anthropic');
    if (!anthropic) throw new Error('Anthropic provider not configured');

    // Convert messages to Anthropic format
    const systemMessage = messages.find(m => m.role === 'system');
    const userMessages = messages.filter(m => m.role !== 'system');

    const response = await anthropic.messages.create({
      model,
      system: systemMessage?.content,
      messages: userMessages.map(msg => ({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content,
      })),
      temperature: options.temperature,
      max_tokens: options.maxTokens,
    });

    return {
      content: response.content[0].type === 'text' ? response.content[0].text : '',
      usage: {
        promptTokens: response.usage.input_tokens,
        completionTokens: response.usage.output_tokens,
        totalTokens: response.usage.input_tokens + response.usage.output_tokens,
      },
      model: response.model,
      finishReason: response.stop_reason || 'stop',
    };
  }

  private async generateGoogle(
    model: string,
    messages: AIMessage[],
    options: any
  ): Promise<AIResponse> {
    const google = this.providers.get('google');
    if (!google) throw new Error('Google provider not configured');

    const genModel = google.getGenerativeModel({ model });
    
    // Convert messages to Google format
    const prompt = messages.map(m => `${m.role}: ${m.content}`).join('\n');
    
    const result = await genModel.generateContent(prompt);
    const response = await result.response;

    return {
      content: response.text(),
      usage: {
        promptTokens: 0, // Google doesn't provide token counts in free tier
        completionTokens: 0,
        totalTokens: 0,
      },
      model,
      finishReason: 'stop',
    };
  }

  private async generateMistral(
    model: string,
    messages: AIMessage[],
    options: any
  ): Promise<AIResponse> {
    const mistral = this.providers.get('mistral');
    if (!mistral) throw new Error('Mistral provider not configured');

    const response = await mistral.chat({
      model,
      messages,
      temperature: options.temperature,
      maxTokens: options.maxTokens,
    });

    return {
      content: response.choices[0].message.content || '',
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0,
      },
      model: response.model,
      finishReason: response.choices[0].finish_reason || 'stop',
    };
  }

  private async generateGroq(
    model: string,
    messages: AIMessage[],
    options: any
  ): Promise<AIResponse> {
    const groq = this.providers.get('groq');
    if (!groq) throw new Error('Groq provider not configured');

    const response = await groq.chat.completions.create({
      model,
      messages,
      temperature: options.temperature,
      max_tokens: options.maxTokens,
    });

    return {
      content: response.choices[0].message.content || '',
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0,
      },
      model: response.model,
      finishReason: response.choices[0].finish_reason || 'stop',
    };
  }

  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  getModelsForProvider(provider: string): string[] {
    switch (provider.toLowerCase()) {
      case 'openai':
        return ['gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo', 'gpt-3.5-turbo-16k'];
      case 'anthropic':
        return ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'];
      case 'google':
        return ['gemini-pro', 'gemini-pro-vision'];
      case 'mistral':
        return ['mistral-large-latest', 'mistral-medium-latest', 'mistral-small-latest'];
      case 'groq':
        return ['llama-3.1-70b-versatile', 'llama-3.1-8b-instant', 'mixtral-8x7b-32768'];
      default:
        return [];
    }
  }

  async validateProvider(provider: string, config: any): Promise<boolean> {
    try {
      const testMessages: AIMessage[] = [
        { role: 'user', content: 'Hello, this is a test message.' }
      ];

      await this.generateResponse(provider, this.getModelsForProvider(provider)[0], testMessages, {
        temperature: 0.1,
        maxTokens: 10,
      });

      return true;
    } catch (error) {
      this.logger.error(`Provider validation failed for ${provider}:`, error);
      return false;
    }
  }
}