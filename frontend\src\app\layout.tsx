import type { <PERSON>ada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from './providers'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'AxientOS - AI Business System Generator',
  description: 'Build complete business systems with AI-powered automation, MCP agents, and dynamic workflows',
  keywords: 'AI, Business Automation, MCP, Workflow Builder, SaaS',
  authors: [{ name: 'AxientOS Team' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}