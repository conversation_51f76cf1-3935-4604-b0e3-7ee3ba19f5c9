import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateWorkflowDto, UpdateWorkflowDto } from './dto/workflow.dto';
import { WorkflowEngineService } from '../execution/workflow-engine.service';

@Injectable()
export class WorkflowsService {
  constructor(
    private prisma: PrismaService,
    private workflowEngine: WorkflowEngineService,
  ) {}

  async create(createWorkflowDto: CreateWorkflowDto, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user?.workspaceId) {
      throw new ForbiddenException('User not associated with a workspace');
    }

    const workflow = await this.prisma.workflow.create({
      data: {
        name: createWorkflowDto.name,
        description: createWorkflowDto.description,
        creatorId: userId,
        workspaceId: user.workspaceId,
        isActive: createWorkflowDto.isActive ?? true,
        isPublic: createWorkflowDto.isPublic ?? false,
      },
    });

    // Create nodes if provided
    if (createWorkflowDto.nodes && createWorkflowDto.nodes.length > 0) {
      await this.updateNodes(workflow.id, createWorkflowDto.nodes);
    }

    // Create edges if provided
    if (createWorkflowDto.edges && createWorkflowDto.edges.length > 0) {
      await this.updateEdges(workflow.id, createWorkflowDto.edges);
    }

    // Create triggers if provided
    if (createWorkflowDto.triggers && createWorkflowDto.triggers.length > 0) {
      await this.updateTriggers(workflow.id, createWorkflowDto.triggers);
    }

    return this.findOne(workflow.id);
  }

  async findAll(workspaceId: string, isPublic?: boolean) {
    const where: any = {
      OR: [
        { workspaceId },
        { isPublic: true },
      ],
    };

    if (isPublic !== undefined) {
      where.isPublic = isPublic;
    }

    return this.prisma.workflow.findMany({
      where,
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        _count: {
          select: { 
            nodes: true,
            executions: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });
  }

  async findOne(id: string) {
    const workflow = await this.prisma.workflow.findUnique({
      where: { id },
      include: {
        creator: {
          select: { id: true, name: true, email: true },
        },
        nodes: {
          include: {
            agent: true,
            tool: true,
          },
        },
        edges: true,
        triggers: true,
        executions: {
          take: 10,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            status: true,
            createdAt: true,
            completedAt: true,
            error: true,
          },
        },
        _count: {
          select: { executions: true },
        },
      },
    });

    if (!workflow) {
      throw new NotFoundException('Workflow not found');
    }

    return workflow;
  }

  async update(id: string, updateWorkflowDto: UpdateWorkflowDto, userId: string) {
    const workflow = await this.prisma.workflow.findUnique({
      where: { id },
    });

    if (!workflow) {
      throw new NotFoundException('Workflow not found');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (workflow.creatorId !== userId && user.role !== 'ADMIN') {
      throw new ForbiddenException('Not authorized to update this workflow');
    }

    // Update basic workflow info
    await this.prisma.workflow.update({
      where: { id },
      data: {
        name: updateWorkflowDto.name,
        description: updateWorkflowDto.description,
        isActive: updateWorkflowDto.isActive,
        isPublic: updateWorkflowDto.isPublic,
        version: { increment: 1 },
      },
    });

    // Update nodes if provided
    if (updateWorkflowDto.nodes !== undefined) {
      await this.updateNodes(id, updateWorkflowDto.nodes);
    }

    // Update edges if provided
    if (updateWorkflowDto.edges !== undefined) {
      await this.updateEdges(id, updateWorkflowDto.edges);
    }

    // Update triggers if provided
    if (updateWorkflowDto.triggers !== undefined) {
      await this.updateTriggers(id, updateWorkflowDto.triggers);
    }

    return this.findOne(id);
  }

  async remove(id: string, userId: string) {
    const workflow = await this.prisma.workflow.findUnique({
      where: { id },
    });

    if (!workflow) {
      throw new NotFoundException('Workflow not found');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (workflow.creatorId !== userId && user.role !== 'ADMIN') {
      throw new ForbiddenException('Not authorized to delete this workflow');
    }

    await this.prisma.workflow.delete({
      where: { id },
    });

    return { message: 'Workflow deleted successfully' };
  }

  async execute(id: string, input: any, userId?: string) {
    const workflow = await this.findOne(id);
    
    if (!workflow.isActive) {
      throw new ForbiddenException('Workflow is not active');
    }

    return this.workflowEngine.executeWorkflow(id, input, userId);
  }

  async getExecutions(id: string, limit: number = 50) {
    return this.prisma.workflowExecution.findMany({
      where: { workflowId: id },
      include: {
        triggeredBy: {
          select: { id: true, name: true, email: true },
        },
        nodeExecutions: true,
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  }

  async getExecutionStatus(executionId: string) {
    return this.workflowEngine.getExecutionStatus(executionId);
  }

  async getWorkflowMetrics(id: string, timeRange: string = '7d') {
    const since = new Date();
    if (timeRange === '24h') {
      since.setHours(since.getHours() - 24);
    } else if (timeRange === '7d') {
      since.setDate(since.getDate() - 7);
    } else if (timeRange === '30d') {
      since.setDate(since.getDate() - 30);
    }

    const executions = await this.prisma.workflowExecution.findMany({
      where: {
        workflowId: id,
        createdAt: { gte: since },
      },
    });

    const total = executions.length;
    const successful = executions.filter(e => e.status === 'COMPLETED').length;
    const failed = executions.filter(e => e.status === 'FAILED').length;
    const avgDuration = executions.length > 0 
      ? executions.reduce((sum, e) => {
          const duration = e.completedAt ? 
            new Date(e.completedAt).getTime() - new Date(e.startedAt).getTime() : 0;
          return sum + duration;
        }, 0) / executions.length
      : 0;

    return {
      totalExecutions: total,
      successfulExecutions: successful,
      failedExecutions: failed,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      averageDuration: Math.round(avgDuration),
      timeRange,
    };
  }

  private async updateNodes(workflowId: string, nodes: any[]) {
    // Delete existing nodes
    await this.prisma.workflowNode.deleteMany({
      where: { workflowId },
    });

    // Create new nodes
    if (nodes.length > 0) {
      const nodeData = nodes.map(node => ({
        id: node.id,
        workflowId,
        type: node.type,
        position: node.position,
        data: node.data,
        agentId: node.data.agentId || null,
        toolId: node.data.toolId || null,
      }));

      await this.prisma.workflowNode.createMany({
        data: nodeData,
      });
    }
  }

  private async updateEdges(workflowId: string, edges: any[]) {
    // Delete existing edges
    await this.prisma.workflowEdge.deleteMany({
      where: { workflowId },
    });

    // Create new edges
    if (edges.length > 0) {
      const edgeData = edges.map(edge => ({
        id: edge.id,
        workflowId,
        sourceNodeId: edge.source,
        targetNodeId: edge.target,
        sourceHandle: edge.sourceHandle || null,
        targetHandle: edge.targetHandle || null,
        condition: edge.data?.condition || null,
      }));

      await this.prisma.workflowEdge.createMany({
        data: edgeData,
      });
    }
  }

  private async updateTriggers(workflowId: string, triggers: any[]) {
    // Delete existing triggers
    await this.prisma.workflowTrigger.deleteMany({
      where: { workflowId },
    });

    // Create new triggers
    if (triggers.length > 0) {
      const triggerData = triggers.map(trigger => ({
        workflowId,
        type: trigger.type,
        config: trigger.config,
      }));

      await this.prisma.workflowTrigger.createMany({
        data: triggerData,
      });
    }
  }
}