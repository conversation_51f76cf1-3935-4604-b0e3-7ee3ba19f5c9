'use client'

import { memo } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Clock } from 'lucide-react'

interface DelayNodeData {
  label: string
  delay: number
}

export const DelayNode = memo(({ data, selected }: NodeProps<DelayNodeData>) => {
  const formatDelay = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${Math.floor(ms / 1000)}s`
    return `${Math.floor(ms / 60000)}m`
  }

  return (
    <div className={`
      relative bg-white dark:bg-gray-800 border-2 rounded-lg shadow-lg min-w-[200px]
      ${selected ? 'border-yellow-500' : 'border-gray-200 dark:border-gray-700'}
      transition-all duration-200 hover:shadow-xl
    `}>
      <Handle type="target" position={Position.Top} className="w-3 h-3" />
      
      <div className="p-4">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
            <Clock className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
              {data.label}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Delay
            </p>
          </div>
        </div>
        
        <div className="mt-2">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
            {formatDelay(data.delay)}
          </span>
        </div>
      </div>
      
      <Handle type="source" position={Position.Bottom} className="w-3 h-3" />
    </div>
  )
})