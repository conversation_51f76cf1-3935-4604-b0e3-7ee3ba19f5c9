import { 
  <PERSON>, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards, 
  Request,
  Query,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { WorkflowsService } from './workflows.service';
import { CreateWorkflowDto, UpdateWorkflowDto } from './dto/workflow.dto';

@Controller('workflows')
@UseGuards(AuthGuard('jwt'))
export class WorkflowsController {
  constructor(private readonly workflowsService: WorkflowsService) {}

  @Post()
  create(@Body() createWorkflowDto: CreateWorkflowDto, @Request() req) {
    return this.workflowsService.create(createWorkflowDto, req.user.id);
  }

  @Get()
  findAll(@Request() req, @Query('public') isPublic?: string) {
    const publicOnly = isPublic === 'true' ? true : undefined;
    return this.workflowsService.findAll(req.user.workspaceId, publicOnly);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.workflowsService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string, 
    @Body() updateWorkflowDto: UpdateWorkflowDto,
    @Request() req
  ) {
    return this.workflowsService.update(id, updateWorkflowDto, req.user.id);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.workflowsService.remove(id, req.user.id);
  }

  @Post(':id/execute')
  execute(
    @Param('id') id: string,
    @Body() input: any,
    @Request() req
  ) {
    return this.workflowsService.execute(id, input, req.user.id);
  }

  @Get(':id/executions')
  getExecutions(@Param('id') id: string, @Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit, 10) : 50;
    return this.workflowsService.getExecutions(id, limitNum);
  }

  @Get('executions/:executionId/status')
  getExecutionStatus(@Param('executionId') executionId: string) {
    return this.workflowsService.getExecutionStatus(executionId);
  }

  @Get(':id/metrics')
  getMetrics(
    @Param('id') id: string,
    @Query('timeRange') timeRange?: string
  ) {
    return this.workflowsService.getWorkflowMetrics(id, timeRange);
  }
}