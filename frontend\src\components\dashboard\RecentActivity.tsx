'use client'

import { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { formatDistanceToNow } from 'date-fns'
import { 
  Bo<PERSON>, 
  Wrench, 
  Workflow, 
  User, 
  Clock, 
  CheckCircle, 
  XCircle,
  Filter,
  RefreshCw
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { apiClient } from '@/lib/api-client'

interface Activity {
  id: string
  type: 'agent' | 'tool' | 'workflow' | 'execution'
  action: 'created' | 'updated' | 'executed' | 'failed' | 'completed'
  title: string
  description: string
  user: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  metadata: Record<string, any>
  createdAt: string
}

const activityIcons = {
  agent: Bo<PERSON>,
  tool: Wrench,
  workflow: Workflow,
  execution: Clock,
}

const actionColors = {
  created: 'bg-blue-100 text-blue-800',
  updated: 'bg-yellow-100 text-yellow-800',
  executed: 'bg-purple-100 text-purple-800',
  completed: 'bg-green-100 text-green-800',
  failed: 'bg-red-100 text-red-800',
}

export function RecentActivity() {
  const [filter, setFilter] = useState('all')
  
  const { data: activities, isLoading, refetch } = useQuery({
    queryKey: ['recent-activity', filter],
    queryFn: async () => {
      const response = await apiClient.get('/api/dashboard/activity', {
        params: { filter, limit: 20 }
      })
      return response.data
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  })

  const getActivityIcon = (type: string, action: string) => {
    const Icon = activityIcons[type] || Clock
    
    if (action === 'completed') return CheckCircle
    if (action === 'failed') return XCircle
    
    return Icon
  }

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Recent Activity</CardTitle>
          <div className="flex items-center gap-2">
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-32">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="agent">Agents</SelectItem>
                <SelectItem value="tool">Tools</SelectItem>
                <SelectItem value="workflow">Workflows</SelectItem>
                <SelectItem value="execution">Executions</SelectItem>
              </SelectContent>
            </Select>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => refetch()}
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-start gap-3 p-3 animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4" />
                  <div className="h-3 bg-gray-200 rounded w-1/2" />
                </div>
                <div className="w-16 h-4 bg-gray-200 rounded" />
              </div>
            ))}
          </div>
        ) : activities?.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No recent activity</p>
          </div>
        ) : (
          <div className="space-y-1">
            {activities?.map((activity: Activity) => {
              const Icon = getActivityIcon(activity.type, activity.action)
              
              return (
                <div 
                  key={activity.id}
                  className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex-shrink-0 mt-0.5">
                    <div className="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                      <Icon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {activity.title}
                      </p>
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${actionColors[activity.action]}`}
                      >
                        {activity.action}
                      </Badge>
                    </div>
                    
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                      {activity.description}
                    </p>
                    
                    <div className="flex items-center gap-2">
                      <Avatar className="w-5 h-5">
                        <AvatarImage src={activity.user.avatar} />
                        <AvatarFallback className="text-xs">
                          {getUserInitials(activity.user.name)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {activity.user.name}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex-shrink-0 text-xs text-gray-500 dark:text-gray-400">
                    {formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}