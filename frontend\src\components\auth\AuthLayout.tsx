import Image from 'next/image'
import Link from 'next/link'
import { Bot } from 'lucide-react'

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  subtitle: string
  showSwitch?: {
    text: string
    linkText: string
    href: string
  }
}

export function AuthLayout({ children, title, subtitle, showSwitch }: AuthLayoutProps) {
  return (
    <div className="min-h-screen flex">
      {/* Left Panel - Form */}
      <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          <div>
            <Link href="/" className="flex items-center gap-3 mb-8">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-gray-900">AxientOS</span>
            </Link>
            
            <h2 className="text-3xl font-bold text-gray-900">{title}</h2>
            <p className="mt-2 text-sm text-gray-600">{subtitle}</p>
          </div>

          <div className="mt-8">
            {children}
          </div>

          {showSwitch && (
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                {showSwitch.text}{' '}
                <Link href={showSwitch.href} className="font-medium text-blue-600 hover:text-blue-500">
                  {showSwitch.linkText}
                </Link>
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Right Panel - Image */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-700">
          <div className="absolute inset-0 bg-black/20" />
          <div className="relative z-10 flex flex-col justify-center items-center h-full p-12 text-white">
            <div className="max-w-md text-center">
              <h3 className="text-3xl font-bold mb-4">
                Build Intelligent Business Systems
              </h3>
              <p className="text-lg opacity-90 mb-8">
                Transform your business operations with AI-powered agents, 
                workflows, and automation tools.
              </p>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold">9</div>
                  <div className="text-sm opacity-75">MCP Agents</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">100+</div>
                  <div className="text-sm opacity-75">Tools</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">∞</div>
                  <div className="text-sm opacity-75">Workflows</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}